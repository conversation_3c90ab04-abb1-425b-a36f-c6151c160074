/**
 * SummaryCard Component
 * 
 * A reusable card component for displaying summary statistics
 * with an icon, label, and count value.
 * 
 * Props:
 * - label: The text label for the statistic
 * - count: The numeric value to display
 * - icon: React component for the icon
 * - variant: Color variant for styling (critical, warning, info, success)
 */

import React from 'react';
import { LucideIcon } from 'lucide-react';

interface SummaryCardProps {
  label: string;
  count: number;
  icon: LucideIcon;
  variant?: 'critical' | 'warning' | 'info' | 'success' | 'default';
}

const variantStyles = {
  critical: 'bg-red-50 border-red-200 text-red-800',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  info: 'bg-blue-50 border-blue-200 text-blue-800',
  success: 'bg-green-50 border-green-200 text-green-800',
  default: 'bg-gray-50 border-gray-200 text-gray-800'
};

const iconStyles = {
  critical: 'text-red-600',
  warning: 'text-yellow-600',
  info: 'text-blue-600',
  success: 'text-green-600',
  default: 'text-gray-600'
};

export default function SummaryCard({ 
  label, 
  count, 
  icon: Icon, 
  variant = 'default' 
}: SummaryCardProps) {
  return (
    <div className={`
      p-6 rounded-lg border-2 transition-all duration-200 hover:shadow-md
      ${variantStyles[variant]}
    `}>
      <div className="flex flex-col space-y-4">
        {/* Top row with icon */}
        <div className="flex items-center justify-between">
          <div className={`
            p-3 rounded-full bg-white/50
            ${iconStyles[variant]}
          `}>
            <Icon className="w-8 h-8" />
          </div>
          <div className="text-right">
            <p className="text-3xl font-bold">
              {count.toLocaleString()}
            </p>
          </div>
        </div>

        {/* Bottom row with label */}
        <div>
          <p className="text-sm font-medium opacity-80 leading-relaxed">
            {label}
          </p>
        </div>
      </div>
    </div>
  );
}
