# Google Maps Integration

This directory contains all Google Maps integration components for the Next.js TTS application.

## 🗺️ Components Overview

### Core Components

#### `InteractiveMap`

The base reusable map component with support for:

- Interactive markers with info windows
- Polylines and polygons
- Circles and overlays
- Marker clustering
- Real-time updates
- Custom map themes

#### `MapLoader`

Handles Google Maps API loading with:

- Error handling and fallbacks
- Loading states
- API key validation
- Configuration management

#### `MapExample`

Basic example demonstrating map usage with sample data.

### Specialized Components

#### `VehicleTrackingMap`

Specialized for vehicle tracking with features:

- Real-time vehicle positions
- Vehicle status indicators
- Route visualization
- Driver information
- Status legend

#### `TripRouteMap`

Designed for trip route visualization:

- Origin and destination markers
- Waypoint display
- Route progress tracking
- Trip alerts
- ETA information

#### `GeofenceManagementMap`

For geofence creation and management:

- Interactive geofence drawing
- Circle and polygon geofences
- Edit mode with drag/resize
- Geofence list management
- Rule configuration

## 🚀 Quick Start

### 1. Environment Setup

**Option A: Follow the Setup Guide**
Visit `/maps-setup` in your application for a step-by-step guide.

**Option B: Manual Setup**
Create a `.env.local` file with your Google Maps API key:

```env
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here
```

**Get your API key:**

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable "Maps JavaScript API"
4. Create credentials → API Key
5. Restrict the key for security

### 2. Basic Usage

```tsx
import { InteractiveMap, MapLoader } from "@/components/maps";

function MyMapPage() {
  return (
    <MapLoader>
      <InteractiveMap
        center={{ lat: 24.7136, lng: 46.6753 }}
        zoom={10}
        height="400px"
        markers={[
          {
            id: "1",
            position: { lat: 24.7136, lng: 46.6753 },
            title: "Riyadh",
          },
        ]}
      />
    </MapLoader>
  );
}
```

### 3. Vehicle Tracking

```tsx
import { VehicleTrackingMap } from "@/components/maps";

function VehicleTrackingPage() {
  const vehicles = [
    {
      id: "1",
      name: "Vehicle 001",
      position: { lat: 24.7136, lng: 46.6753 },
      status: "moving",
      speed: 60,
      // ... other vehicle properties
    },
  ];

  return (
    <VehicleTrackingMap
      center={{ lat: 24.7136, lng: 46.6753 }}
      zoom={12}
      vehicles={vehicles}
      showRoutes={true}
      realTimeUpdates={true}
    />
  );
}
```

## 📋 Configuration

### Map Configuration

The `GOOGLE_MAPS_CONFIG` object in `lib/maps/config.ts` contains:

- API key and libraries
- Default center and zoom
- Map styling options
- Performance settings
- Error messages

### Customization

You can customize maps by:

1. **Themes**: Use predefined themes (`default`, `dark`, `minimal`)
2. **Styling**: Pass custom `mapStyles` prop
3. **Controls**: Enable/disable map controls
4. **Clustering**: Configure marker clustering options

## 🎨 Styling

### Map Themes

```tsx
// Dark theme
<InteractiveMap theme="dark" />

// Custom styles
<InteractiveMap
  mapStyles={[
    {
      featureType: "poi",
      elementType: "labels",
      stylers: [{ visibility: "off" }]
    }
  ]}
/>
```

### Vehicle Status Colors

Defined in `VEHICLE_STATUS_COLORS`:

- `online`: Green (#4CAF50)
- `offline`: Red (#F44336)
- `idle`: Orange (#FF9800)
- `moving`: Blue (#2196F3)
- `maintenance`: Purple (#9C27B0)

## 🔧 API Reference

### InteractiveMap Props

| Prop               | Type                               | Default     | Description                 |
| ------------------ | ---------------------------------- | ----------- | --------------------------- |
| `center`           | `MapPosition`                      | Required    | Map center coordinates      |
| `zoom`             | `number`                           | Required    | Initial zoom level          |
| `markers`          | `MapMarker[]`                      | `[]`        | Array of markers to display |
| `polylines`        | `MapPolyline[]`                    | `[]`        | Array of polylines          |
| `polygons`         | `MapPolygon[]`                     | `[]`        | Array of polygons           |
| `enableClustering` | `boolean`                          | `false`     | Enable marker clustering    |
| `realTimeUpdates`  | `boolean`                          | `false`     | Enable real-time updates    |
| `theme`            | `'default' \| 'dark' \| 'minimal'` | `'default'` | Map theme                   |

### Vehicle Interface

```typescript
interface Vehicle {
  id: string;
  name: string;
  position: MapPosition;
  heading: number;
  speed: number;
  status: "online" | "offline" | "idle" | "moving" | "maintenance";
  lastUpdate: Date;
  route?: MapPosition[];
  driver?: {
    name: string;
    id: string;
  };
}
```

## 🧪 Testing

Visit `/maps-test` to see all components in action with sample data.

The test page includes:

- Basic map functionality
- Vehicle tracking simulation
- Trip route visualization
- Geofence management tools

## 🔒 Security

### API Key Security

- Use environment variables for API keys
- Configure API key restrictions in Google Cloud Console
- Limit usage by HTTP referrers or IP addresses

### Best Practices

1. **Never expose API keys** in client-side code
2. **Set up billing alerts** to monitor usage
3. **Use API key restrictions** for production
4. **Monitor API usage** regularly

## 📱 Mobile Support

All components are responsive and mobile-friendly:

- Touch gestures for pan/zoom
- Responsive layouts
- Mobile-optimized controls
- Offline capability (where supported)

## 🚀 Performance

### Optimization Features

- Marker clustering for large datasets
- Debounced real-time updates
- Lazy loading of map components
- Efficient re-rendering strategies

### Best Practices

1. **Use clustering** for >100 markers
2. **Limit real-time updates** to necessary intervals
3. **Optimize marker icons** (use appropriate sizes)
4. **Consider viewport bounds** for data loading

## 🐛 Troubleshooting

### Common Issues

1. **Map not loading**: Check API key configuration
2. **Markers not appearing**: Verify marker data format
3. **Performance issues**: Enable clustering for large datasets
4. **Mobile issues**: Check gesture handling settings

### Debug Mode

Enable debug mode by setting `NODE_ENV=development` to see:

- Console logs for map events
- Error messages
- Performance metrics

## 📚 Resources

- [Google Maps JavaScript API](https://developers.google.com/maps/documentation/javascript)
- [@react-google-maps/api Documentation](https://react-google-maps-api-docs.netlify.app/)
- [Google Maps Styling Guide](https://developers.google.com/maps/documentation/javascript/styling)

## 🤝 Contributing

When adding new map features:

1. Follow existing component patterns
2. Add TypeScript interfaces
3. Include error handling
4. Add to the test page
5. Update documentation
