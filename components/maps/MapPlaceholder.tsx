'use client';

import React from 'react';

/**
 * Map Placeholder Component
 * Shows a placeholder when Google Maps API key is not configured
 */
interface MapPlaceholderProps {
  width?: string | number;
  height?: string | number;
  className?: string;
}

const MapPlaceholder: React.FC<MapPlaceholderProps> = ({
  width = '100%',
  height = '400px',
  className = '',
}) => {
  return (
    <div 
      className={`map-placeholder bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center ${className}`}
      style={{ width, height }}
    >
      <div className="text-center p-8">
        {/* Map Icon */}
        <div className="mb-4">
          <svg 
            className="w-16 h-16 mx-auto text-gray-400" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={1.5} 
              d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" 
            />
          </svg>
        </div>

        {/* Title */}
        <h3 className="text-lg font-semibold text-gray-700 mb-2">
          Google Maps Integration
        </h3>

        {/* Description */}
        <p className="text-gray-600 mb-4 max-w-md">
          To see the interactive map, please configure your Google Maps API key in the environment variables.
        </p>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left max-w-lg">
          <h4 className="font-semibold text-blue-900 mb-2">Setup Instructions:</h4>
          <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
            <li>Get a Google Maps API key from Google Cloud Console</li>
            <li>Add it to your <code className="bg-blue-100 px-1 rounded">.env.local</code> file:</li>
          </ol>
          <div className="mt-2 bg-blue-100 p-2 rounded font-mono text-xs">
            NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_key_here
          </div>
          <p className="text-xs text-blue-600 mt-2">
            Then restart your development server.
          </p>
        </div>

        {/* Sample Features */}
        <div className="mt-6 grid grid-cols-2 gap-4 text-sm">
          <div className="bg-green-50 p-3 rounded-lg">
            <h5 className="font-medium text-green-900 mb-1">Features Ready:</h5>
            <ul className="text-green-700 text-xs space-y-1">
              <li>✓ Interactive markers</li>
              <li>✓ Vehicle tracking</li>
              <li>✓ Trip routes</li>
              <li>✓ Geofences</li>
            </ul>
          </div>
          <div className="bg-yellow-50 p-3 rounded-lg">
            <h5 className="font-medium text-yellow-900 mb-1">APIs Needed:</h5>
            <ul className="text-yellow-700 text-xs space-y-1">
              <li>• Maps JavaScript API</li>
              <li>• Places API (optional)</li>
              <li>• Geocoding API (optional)</li>
            </ul>
          </div>
        </div>

        {/* Mock Data Preview */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h5 className="font-medium text-gray-900 mb-2">Sample Locations:</h5>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-xs">
            <div className="bg-white p-2 rounded border">
              <div className="font-medium text-red-600">📍 Riyadh</div>
              <div className="text-gray-500">24.7136, 46.6753</div>
            </div>
            <div className="bg-white p-2 rounded border">
              <div className="font-medium text-green-600">📍 Mecca</div>
              <div className="text-gray-500">21.3891, 39.8579</div>
            </div>
            <div className="bg-white p-2 rounded border">
              <div className="font-medium text-blue-600">📍 Hail</div>
              <div className="text-gray-500">26.3351, 43.9681</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapPlaceholder;
