/**
 * Maps Components Export Index
 * Centralized exports for all map-related components
 */

// Core map components
export { default as InteractiveMap } from './InteractiveMap';
export { default as MapLoader } from './MapLoader';
export { default as MapExample } from './MapExample';

// Specialized map components
export { default as VehicleTrackingMap } from './VehicleTrackingMap';
export { default as TripRouteMap } from './TripRouteMap';
export { default as GeofenceManagementMap } from './GeofenceManagementMap';

// Types and configurations
export * from '@/lib/maps/types';
export * from '@/lib/maps/config';
