"use client";

import React, { useState, useEffect } from "react";
import { LoadScript } from "@react-google-maps/api";
import { GOOGLE_MAPS_CONFIG, validateMapsConfig } from "@/lib/maps/config";
import MapPlaceholder from "./MapPlaceholder";

interface MapLoaderProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onLoadError?: (error: Error) => void;
}

/**
 * Map Loader Component
 * Handles Google Maps API loading with error handling and fallbacks
 */
const MapLoader: React.FC<MapLoaderProps> = ({
  children,
  fallback,
  onLoadError,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  // Validate configuration on mount
  useEffect(() => {
    if (!validateMapsConfig()) {
      const configError = new Error(
        GOOGLE_MAPS_CONFIG.errorMessages.apiKeyMissing
      );
      setError(configError.message);
      setIsLoading(false);

      if (onLoadError) {
        onLoadError(configError);
      }
    }
  }, [onLoadError]);

  // Handle successful load
  const handleLoad = () => {
    setIsLoading(false);
    setIsLoaded(true);
    setError(null);
  };

  // Handle load error
  const handleError = (error: Error) => {
    setIsLoading(false);
    setIsLoaded(false);
    setError(error.message || GOOGLE_MAPS_CONFIG.errorMessages.loadFailed);

    if (onLoadError) {
      onLoadError(error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      fallback || (
        <div className="map-loader">
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading Google Maps...</span>
          </div>
        </div>
      )
    );
  }

  // Error state
  if (error) {
    return (
      <div className="map-loader-error">
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="text-red-600 mb-4">
            <svg
              className="w-12 h-12 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Failed to Load Maps
          </h3>
          <p className="text-gray-600 mb-4 max-w-md">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Configuration error (no API key) - show placeholder
  if (!GOOGLE_MAPS_CONFIG.apiKey) {
    return <MapPlaceholder height="400px" />;
  }

  // Successfully loaded
  return (
    <LoadScript
      googleMapsApiKey={GOOGLE_MAPS_CONFIG.apiKey}
      libraries={["places", "geometry", "drawing"]}
      region={GOOGLE_MAPS_CONFIG.region}
      language={GOOGLE_MAPS_CONFIG.language}
      onLoad={handleLoad}
      onError={handleError}
      loadingElement={
        fallback || (
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading Google Maps...</span>
          </div>
        )
      }
    >
      {isLoaded ? children : null}
    </LoadScript>
  );
};

export default MapLoader;
