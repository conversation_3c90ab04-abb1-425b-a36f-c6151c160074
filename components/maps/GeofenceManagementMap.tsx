'use client';

import React, { useState, useMemo, useCallback } from 'react';
import InteractiveMap from './InteractiveMap';
import { GeofenceMapProps, Geofence, MapPolygon, MapCircle, MapMarker } from '@/lib/maps/types';

/**
 * Geofence Management Map Component
 * Specialized map for creating, editing, and managing geofences
 */
const GeofenceManagementMap: React.FC<GeofenceMapProps> = ({
  geofences,
  editMode = false,
  selectedGeofence,
  onGeofenceCreate,
  onGeofenceEdit,
  onGeofenceDelete,
  onGeofenceSelect,
  drawingMode = null,
  showGeofenceInfo = true,
  ...mapProps
}) => {
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawingPath, setDrawingPath] = useState<google.maps.LatLngLiteral[]>([]);

  // Convert geofences to map overlays
  const geofenceOverlays = useMemo(() => {
    const polygons: MapPolygon[] = [];
    const circles: MapCircle[] = [];
    const markers: MapMarker[] = [];

    geofences.forEach((geofence) => {
      const isSelected = selectedGeofence === geofence.id;
      const opacity = geofence.active ? (isSelected ? 0.6 : 0.4) : 0.2;
      const strokeWeight = isSelected ? 3 : 2;

      if (geofence.type === 'polygon') {
        polygons.push({
          id: geofence.id,
          paths: geofence.coordinates,
          strokeColor: geofence.strokeColor,
          strokeOpacity: 1.0,
          strokeWeight,
          fillColor: geofence.color,
          fillOpacity: opacity,
          editable: editMode && isSelected,
          draggable: editMode && isSelected,
          onClick: () => onGeofenceSelect?.(geofence),
          data: geofence,
        });
      } else if (geofence.type === 'circle' && geofence.radius) {
        const center = geofence.coordinates[0];
        circles.push({
          id: geofence.id,
          center,
          radius: geofence.radius,
          strokeColor: geofence.strokeColor,
          strokeOpacity: 1.0,
          strokeWeight,
          fillColor: geofence.color,
          fillOpacity: opacity,
          editable: editMode && isSelected,
          draggable: editMode && isSelected,
          onClick: () => onGeofenceSelect?.(geofence),
          data: geofence,
        });
      }

      // Add center marker for identification
      if (showGeofenceInfo) {
        const center = geofence.type === 'circle' 
          ? geofence.coordinates[0]
          : calculatePolygonCenter(geofence.coordinates);

        markers.push({
          id: `${geofence.id}-marker`,
          position: center,
          title: geofence.name,
          icon: {
            url: geofence.active ? '/icons/geofence-active.png' : '/icons/geofence-inactive.png',
            scaledSize: new google.maps.Size(24, 24),
            anchor: new google.maps.Point(12, 12),
          },
          infoWindow: {
            content: (
              <GeofenceInfoWindow 
                geofence={geofence}
                onEdit={() => onGeofenceEdit?.(geofence)}
                onDelete={() => onGeofenceDelete?.(geofence.id)}
                editMode={editMode}
              />
            ),
          },
          onClick: () => onGeofenceSelect?.(geofence),
        });
      }
    });

    return { polygons, circles, markers };
  }, [geofences, selectedGeofence, editMode, showGeofenceInfo, onGeofenceSelect, onGeofenceEdit, onGeofenceDelete]);

  // Handle map click for drawing
  const handleMapClick = useCallback((event: google.maps.MapMouseEvent) => {
    if (!editMode || !drawingMode || !event.latLng) return;

    const position = {
      lat: event.latLng.lat(),
      lng: event.latLng.lng(),
    };

    if (drawingMode === 'circle') {
      // For circle, we need two clicks: center and radius point
      if (drawingPath.length === 0) {
        setDrawingPath([position]);
        setIsDrawing(true);
      } else if (drawingPath.length === 1) {
        const center = drawingPath[0];
        const radius = calculateDistance(center, position);
        
        const newGeofence: Omit<Geofence, 'id' | 'createdAt' | 'updatedAt'> = {
          name: `Geofence ${geofences.length + 1}`,
          description: 'New circular geofence',
          type: 'circle',
          coordinates: [center],
          radius,
          color: '#2196F3',
          strokeColor: '#1976D2',
          fillOpacity: 0.4,
          strokeWeight: 2,
          active: true,
          rules: {
            alertOnEntry: true,
            alertOnExit: true,
          },
        };

        onGeofenceCreate?.(newGeofence);
        setDrawingPath([]);
        setIsDrawing(false);
      }
    } else if (drawingMode === 'polygon') {
      setDrawingPath(prev => [...prev, position]);
      setIsDrawing(true);
    }
  }, [editMode, drawingMode, drawingPath, geofences.length, onGeofenceCreate]);

  // Handle double click to finish polygon drawing
  const handleMapDoubleClick = useCallback(() => {
    if (!editMode || drawingMode !== 'polygon' || drawingPath.length < 3) return;

    const newGeofence: Omit<Geofence, 'id' | 'createdAt' | 'updatedAt'> = {
      name: `Geofence ${geofences.length + 1}`,
      description: 'New polygon geofence',
      type: 'polygon',
      coordinates: drawingPath,
      color: '#2196F3',
      strokeColor: '#1976D2',
      fillOpacity: 0.4,
      strokeWeight: 2,
      active: true,
      rules: {
        alertOnEntry: true,
        alertOnExit: true,
      },
    };

    onGeofenceCreate?.(newGeofence);
    setDrawingPath([]);
    setIsDrawing(false);
  }, [editMode, drawingMode, drawingPath, geofences.length, onGeofenceCreate]);

  // Helper function to calculate polygon center
  function calculatePolygonCenter(coordinates: google.maps.LatLngLiteral[]): google.maps.LatLngLiteral {
    const totalLat = coordinates.reduce((sum, coord) => sum + coord.lat, 0);
    const totalLng = coordinates.reduce((sum, coord) => sum + coord.lng, 0);
    
    return {
      lat: totalLat / coordinates.length,
      lng: totalLng / coordinates.length,
    };
  }

  // Helper function to calculate distance between two points
  function calculateDistance(point1: google.maps.LatLngLiteral, point2: google.maps.LatLngLiteral): number {
    const R = 6371000; // Earth's radius in meters
    const dLat = (point2.lat - point1.lat) * Math.PI / 180;
    const dLng = (point2.lng - point1.lng) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  return (
    <div className="geofence-management-map relative">
      <InteractiveMap
        {...mapProps}
        markers={geofenceOverlays.markers}
        polygons={geofenceOverlays.polygons}
        circles={geofenceOverlays.circles}
        onClick={handleMapClick}
        onDoubleClick={handleMapDoubleClick}
        gestureHandling={editMode ? 'greedy' : 'auto'}
      />
      
      {/* Drawing Tools */}
      {editMode && (
        <GeofenceDrawingTools
          drawingMode={drawingMode}
          isDrawing={isDrawing}
          drawingPath={drawingPath}
          onCancelDrawing={() => {
            setDrawingPath([]);
            setIsDrawing(false);
          }}
        />
      )}
      
      {/* Geofence List */}
      <GeofenceList
        geofences={geofences}
        selectedGeofence={selectedGeofence}
        onSelect={onGeofenceSelect}
        onDelete={onGeofenceDelete}
        editMode={editMode}
      />
    </div>
  );
};

/**
 * Geofence Info Window Component
 */
interface GeofenceInfoWindowProps {
  geofence: Geofence;
  onEdit?: () => void;
  onDelete?: () => void;
  editMode: boolean;
}

const GeofenceInfoWindow: React.FC<GeofenceInfoWindowProps> = ({
  geofence,
  onEdit,
  onDelete,
  editMode,
}) => {
  return (
    <div className="geofence-info-window p-3 min-w-[250px]">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-semibold text-lg text-gray-900">{geofence.name}</h3>
        <span 
          className={`px-2 py-1 rounded-full text-xs font-medium ${
            geofence.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}
        >
          {geofence.active ? 'ACTIVE' : 'INACTIVE'}
        </span>
      </div>
      
      {geofence.description && (
        <p className="text-sm text-gray-600 mb-3">{geofence.description}</p>
      )}
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Type:</span>
          <span className="font-medium capitalize">{geofence.type}</span>
        </div>
        
        {geofence.type === 'circle' && geofence.radius && (
          <div className="flex justify-between">
            <span className="text-gray-600">Radius:</span>
            <span className="font-medium">{(geofence.radius / 1000).toFixed(2)} km</span>
          </div>
        )}
        
        <div className="flex justify-between">
          <span className="text-gray-600">Points:</span>
          <span className="font-medium">{geofence.coordinates.length}</span>
        </div>
        
        {geofence.rules && (
          <div className="pt-2 border-t">
            <div className="text-xs text-gray-600 space-y-1">
              {geofence.rules.alertOnEntry && (
                <div>✓ Alert on entry</div>
              )}
              {geofence.rules.alertOnExit && (
                <div>✓ Alert on exit</div>
              )}
            </div>
          </div>
        )}
      </div>
      
      {editMode && (
        <div className="flex gap-2 mt-3">
          {onEdit && (
            <button
              onClick={onEdit}
              className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Edit
            </button>
          )}
          {onDelete && (
            <button
              onClick={onDelete}
              className="flex-1 px-3 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              Delete
            </button>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Drawing Tools Component
 */
interface GeofenceDrawingToolsProps {
  drawingMode: 'circle' | 'polygon' | null;
  isDrawing: boolean;
  drawingPath: google.maps.LatLngLiteral[];
  onCancelDrawing: () => void;
}

const GeofenceDrawingTools: React.FC<GeofenceDrawingToolsProps> = ({
  drawingMode,
  isDrawing,
  drawingPath,
  onCancelDrawing,
}) => {
  if (!drawingMode) return null;

  return (
    <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-4">
      <h4 className="font-semibold text-gray-900 mb-2">
        Drawing {drawingMode === 'circle' ? 'Circle' : 'Polygon'}
      </h4>
      
      {drawingMode === 'circle' && (
        <p className="text-sm text-gray-600 mb-3">
          {drawingPath.length === 0 
            ? 'Click to set center point'
            : 'Click to set radius'
          }
        </p>
      )}
      
      {drawingMode === 'polygon' && (
        <p className="text-sm text-gray-600 mb-3">
          Click to add points. Double-click to finish.
          <br />
          Points: {drawingPath.length}
        </p>
      )}
      
      {isDrawing && (
        <button
          onClick={onCancelDrawing}
          className="w-full px-3 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          Cancel Drawing
        </button>
      )}
    </div>
  );
};

/**
 * Geofence List Component
 */
interface GeofenceListProps {
  geofences: Geofence[];
  selectedGeofence?: string;
  onSelect?: (geofence: Geofence | null) => void;
  onDelete?: (geofenceId: string) => void;
  editMode: boolean;
}

const GeofenceList: React.FC<GeofenceListProps> = ({
  geofences,
  selectedGeofence,
  onSelect,
  onDelete,
  editMode,
}) => {
  return (
    <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-4 max-w-[300px] max-h-[300px] overflow-y-auto">
      <h4 className="font-semibold text-gray-900 mb-3">
        Geofences ({geofences.length})
      </h4>
      
      <div className="space-y-2">
        {geofences.map((geofence) => (
          <div
            key={geofence.id}
            className={`p-2 rounded cursor-pointer transition-colors ${
              selectedGeofence === geofence.id
                ? 'bg-blue-100 border border-blue-300'
                : 'bg-gray-50 hover:bg-gray-100'
            }`}
            onClick={() => onSelect?.(geofence)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div
                  className="w-3 h-3 rounded-full mr-2"
                  style={{ backgroundColor: geofence.color }}
                />
                <span className="text-sm font-medium truncate">
                  {geofence.name}
                </span>
              </div>
              
              {editMode && onDelete && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete(geofence.id);
                  }}
                  className="text-red-600 hover:text-red-800 p-1"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>
            
            <div className="text-xs text-gray-500 mt-1">
              {geofence.type} • {geofence.active ? 'Active' : 'Inactive'}
            </div>
          </div>
        ))}
        
        {geofences.length === 0 && (
          <p className="text-sm text-gray-500 text-center py-4">
            No geofences created yet
          </p>
        )}
      </div>
    </div>
  );
};

export default GeofenceManagementMap;
