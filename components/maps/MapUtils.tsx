'use client';

/**
 * Map Utilities
 * Helper functions and utilities for map components
 */

// Simple icon configuration without google.maps dependencies
export interface SimpleIcon {
  url: string;
  size?: { width: number; height: number };
  anchor?: { x: number; y: number };
}

/**
 * Convert simple icon to Google Maps icon when google is available
 */
export const createMapIcon = (icon: string | SimpleIcon): string | google.maps.Icon => {
  if (typeof icon === 'string') {
    return icon;
  }

  // If google.maps is not available, return just the URL
  if (typeof window === 'undefined' || !window.google?.maps) {
    return icon.url;
  }

  // Create proper Google Maps icon
  const mapIcon: google.maps.Icon = {
    url: icon.url,
  };

  if (icon.size) {
    mapIcon.scaledSize = new google.maps.Size(icon.size.width, icon.size.height);
  }

  if (icon.anchor) {
    mapIcon.anchor = new google.maps.Point(icon.anchor.x, icon.anchor.y);
  }

  return mapIcon;
};

/**
 * Create animation when google.maps is available
 */
export const createMapAnimation = (animationType: 'bounce' | 'drop' | null): google.maps.Animation | undefined => {
  if (!animationType || typeof window === 'undefined' || !window.google?.maps) {
    return undefined;
  }

  switch (animationType) {
    case 'bounce':
      return google.maps.Animation.BOUNCE;
    case 'drop':
      return google.maps.Animation.DROP;
    default:
      return undefined;
  }
};

/**
 * Calculate distance between two points (Haversine formula)
 */
export const calculateDistance = (
  point1: { lat: number; lng: number },
  point2: { lat: number; lng: number }
): number => {
  const R = 6371000; // Earth's radius in meters
  const dLat = ((point2.lat - point1.lat) * Math.PI) / 180;
  const dLng = ((point2.lng - point1.lng) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((point1.lat * Math.PI) / 180) *
      Math.cos((point2.lat * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

/**
 * Calculate center point of multiple coordinates
 */
export const calculateCenter = (
  coordinates: { lat: number; lng: number }[]
): { lat: number; lng: number } => {
  if (coordinates.length === 0) {
    return { lat: 0, lng: 0 };
  }

  const totalLat = coordinates.reduce((sum, coord) => sum + coord.lat, 0);
  const totalLng = coordinates.reduce((sum, coord) => sum + coord.lng, 0);

  return {
    lat: totalLat / coordinates.length,
    lng: totalLng / coordinates.length,
  };
};

/**
 * Calculate bounds for multiple coordinates
 */
export const calculateBounds = (
  coordinates: { lat: number; lng: number }[]
): {
  north: number;
  south: number;
  east: number;
  west: number;
} => {
  if (coordinates.length === 0) {
    return { north: 0, south: 0, east: 0, west: 0 };
  }

  let north = coordinates[0].lat;
  let south = coordinates[0].lat;
  let east = coordinates[0].lng;
  let west = coordinates[0].lng;

  coordinates.forEach((coord) => {
    north = Math.max(north, coord.lat);
    south = Math.min(south, coord.lat);
    east = Math.max(east, coord.lng);
    west = Math.min(west, coord.lng);
  });

  return { north, south, east, west };
};

/**
 * Check if a point is inside a polygon
 */
export const isPointInPolygon = (
  point: { lat: number; lng: number },
  polygon: { lat: number; lng: number }[]
): boolean => {
  let inside = false;
  const x = point.lng;
  const y = point.lat;

  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    const xi = polygon[i].lng;
    const yi = polygon[i].lat;
    const xj = polygon[j].lng;
    const yj = polygon[j].lat;

    if (yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi) {
      inside = !inside;
    }
  }

  return inside;
};

/**
 * Format coordinates for display
 */
export const formatCoordinates = (
  position: { lat: number; lng: number },
  precision: number = 6
): string => {
  return `${position.lat.toFixed(precision)}, ${position.lng.toFixed(precision)}`;
};

/**
 * Convert degrees to radians
 */
export const degreesToRadians = (degrees: number): number => {
  return (degrees * Math.PI) / 180;
};

/**
 * Convert radians to degrees
 */
export const radiansToDegrees = (radians: number): number => {
  return (radians * 180) / Math.PI;
};

/**
 * Calculate bearing between two points
 */
export const calculateBearing = (
  start: { lat: number; lng: number },
  end: { lat: number; lng: number }
): number => {
  const startLat = degreesToRadians(start.lat);
  const startLng = degreesToRadians(start.lng);
  const endLat = degreesToRadians(end.lat);
  const endLng = degreesToRadians(end.lng);

  const dLng = endLng - startLng;

  const y = Math.sin(dLng) * Math.cos(endLat);
  const x =
    Math.cos(startLat) * Math.sin(endLat) -
    Math.sin(startLat) * Math.cos(endLat) * Math.cos(dLng);

  const bearing = Math.atan2(y, x);
  return (radiansToDegrees(bearing) + 360) % 360;
};

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle function for performance optimization
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};
