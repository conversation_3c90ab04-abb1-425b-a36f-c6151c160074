"use client";

import React, { useState } from "react";
import InteractiveMap from "./InteractiveMap";
import MapLoader from "./MapLoader";
import { MapMarker, MapPosition } from "@/lib/maps/types";
import { GOOGLE_MAPS_CONFIG } from "@/lib/maps/config";

/**
 * Map Example Component
 * Demonstrates basic usage of the InteractiveMap component
 */
const MapExample: React.FC = () => {
  const [selectedMarker, setSelectedMarker] = useState<MapMarker | null>(null);
  const [mapCenter, setMapCenter] = useState<MapPosition>(
    GOOGLE_MAPS_CONFIG.defaultCenter
  );
  const [mapZoom, setMapZoom] = useState<number>(
    GOOGLE_MAPS_CONFIG.defaultZoom
  );

  // Sample markers data
  const sampleMarkers: MapMarker[] = [
    {
      id: "1",
      position: { lat: 24.7136, lng: 46.6753 }, // Riyadh
      title: "Riyadh - Capital",
      infoWindow: {
        content: (
          <div className="p-2">
            <h3 className="font-semibold text-lg">Riyadh</h3>
            <p className="text-gray-600">Capital of Saudi Arabia</p>
            <p className="text-sm text-gray-500 mt-1">
              Coordinates: 24.7136°N, 46.6753°E
            </p>
          </div>
        ),
      },
      icon: "/icons/marker-red.png",
    },
    {
      id: "2",
      position: { lat: 21.3891, lng: 39.8579 }, // Mecca
      title: "Mecca - Holy City",
      infoWindow: {
        content: (
          <div className="p-2">
            <h3 className="font-semibold text-lg">Mecca</h3>
            <p className="text-gray-600">Holy City</p>
            <p className="text-sm text-gray-500 mt-1">
              Coordinates: 21.3891°N, 39.8579°E
            </p>
          </div>
        ),
      },
      icon: "/icons/marker-green.png",
    },
    {
      id: "3",
      position: { lat: 26.3351, lng: 43.9681 }, // Hail
      title: "Hail - Northern Region",
      infoWindow: {
        content: (
          <div className="p-2">
            <h3 className="font-semibold text-lg">Hail</h3>
            <p className="text-gray-600">Northern Region</p>
            <p className="text-sm text-gray-500 mt-1">
              Coordinates: 26.3351°N, 43.9681°E
            </p>
          </div>
        ),
      },
      icon: "/icons/marker-blue.png",
    },
  ];

  // Handle marker click
  const handleMarkerClick = (marker: MapMarker) => {
    setSelectedMarker(marker);
    console.log("Marker clicked:", marker);
  };

  // Handle map click
  const handleMapClick = (event: google.maps.MapMouseEvent) => {
    if (event.latLng) {
      const position = {
        lat: event.latLng.lat(),
        lng: event.latLng.lng(),
      };
      console.log("Map clicked at:", position);
    }
  };

  // Handle bounds changed
  const handleBoundsChanged = (bounds: google.maps.LatLngBounds) => {
    console.log("Map bounds changed:", bounds.toJSON());
  };

  // Handle zoom changed
  const handleZoomChanged = (zoom: number) => {
    setMapZoom(zoom);
    console.log("Map zoom changed:", zoom);
  };

  return (
    <div className="map-example">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Interactive Map Example
        </h2>
        <p className="text-gray-600">
          This example demonstrates the basic usage of our InteractiveMap
          component with sample markers across Saudi Arabia.
        </p>
      </div>

      {/* Map Controls */}
      <div className="mb-4 p-4 bg-gray-50 rounded-lg">
        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">Center:</label>
            <span className="text-sm text-gray-600">
              {mapCenter.lat.toFixed(4)}, {mapCenter.lng.toFixed(4)}
            </span>
          </div>

          <div className="flex items-center gap-2">
            <label className="text-sm font-medium text-gray-700">Zoom:</label>
            <span className="text-sm text-gray-600">{mapZoom}</span>
          </div>

          {selectedMarker && (
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">
                Selected:
              </label>
              <span className="text-sm text-blue-600">
                {selectedMarker.title}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Map Container */}
      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        <MapLoader
          fallback={
            <div className="flex items-center justify-center h-96 bg-gray-50">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                <p className="text-gray-600">Loading map example...</p>
              </div>
            </div>
          }
          onLoadError={(error) => {
            console.error("Map load error:", error);
          }}
        >
          <InteractiveMap
            center={mapCenter}
            zoom={mapZoom}
            height="500px"
            markers={sampleMarkers}
            onMarkerClick={handleMarkerClick}
            onClick={handleMapClick}
            onBoundsChanged={handleBoundsChanged}
            onZoomChanged={handleZoomChanged}
            enableClustering={true}
            showZoomControl={true}
            showMapTypeControl={true}
            showStreetViewControl={true}
            showFullscreenControl={true}
            theme="default"
            className="map-example-container"
          />
        </MapLoader>
      </div>

      {/* Map Information */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">
            Features Demonstrated
          </h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Interactive markers with info windows</li>
            <li>• Map click event handling</li>
            <li>• Zoom and bounds change detection</li>
            <li>• Marker clustering</li>
            <li>• Custom marker icons</li>
            <li>• Responsive design</li>
          </ul>
        </div>

        <div className="p-4 bg-green-50 rounded-lg">
          <h3 className="font-semibold text-green-900 mb-2">
            Sample Locations
          </h3>
          <ul className="text-sm text-green-800 space-y-1">
            <li>• Riyadh (Capital) - Red marker</li>
            <li>• Mecca (Holy City) - Green marker</li>
            <li>• Hail (Northern Region) - Blue marker</li>
          </ul>
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="font-semibold text-yellow-900 mb-2">
          Usage Instructions
        </h3>
        <div className="text-sm text-yellow-800 space-y-1">
          <p>1. Click on markers to see info windows</p>
          <p>2. Click anywhere on the map to see coordinates in console</p>
          <p>3. Use map controls to zoom and change map type</p>
          <p>4. Watch the controls above update as you interact with the map</p>
        </div>
      </div>
    </div>
  );
};

export default MapExample;
