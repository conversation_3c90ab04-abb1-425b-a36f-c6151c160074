'use client';

import React, { useEffect, useRef, useState } from 'react';

/**
 * Direct Map Test Component
 * Tests Google Maps loading without react-google-maps wrapper
 */
const DirectMapTest: React.FC = () => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      setStatus('error');
      setError('API key not found');
      return;
    }

    // Check if Google Maps is already loaded
    if ((window as any).google?.maps) {
      initializeMap();
      return;
    }

    // Load Google Maps script directly
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,geometry,drawing&callback=initMap`;
    script.async = true;
    script.defer = true;

    // Create global callback
    (window as any).initMap = () => {
      console.log('Google Maps loaded successfully');
      initializeMap();
    };

    script.onerror = (e) => {
      console.error('Failed to load Google Maps script:', e);
      setStatus('error');
      setError('Failed to load Google Maps script. Check console for details.');
    };

    document.head.appendChild(script);

    return () => {
      // Cleanup
      const existingScript = document.querySelector(`script[src*="maps.googleapis.com"]`);
      if (existingScript) {
        existingScript.remove();
      }
      delete (window as any).initMap;
    };
  }, []);

  const initializeMap = () => {
    if (!mapRef.current || !(window as any).google?.maps) {
      setStatus('error');
      setError('Google Maps API not available');
      return;
    }

    try {
      const map = new (window as any).google.maps.Map(mapRef.current, {
        center: { lat: 24.7136, lng: 46.6753 }, // Riyadh
        zoom: 10,
        mapTypeId: 'roadmap',
      });

      // Add a simple marker
      new (window as any).google.maps.Marker({
        position: { lat: 24.7136, lng: 46.6753 },
        map: map,
        title: 'Riyadh - Test Marker',
      });

      setStatus('success');
      console.log('Map initialized successfully');
    } catch (err) {
      console.error('Error initializing map:', err);
      setStatus('error');
      setError(`Map initialization failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
    }
  };

  const getStatusDisplay = () => {
    switch (status) {
      case 'loading':
        return (
          <div className="flex items-center justify-center h-64 bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <p className="text-blue-600">Loading Google Maps...</p>
            </div>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center justify-center h-64 bg-red-50 border-2 border-dashed border-red-300 rounded-lg">
            <div className="text-center p-4">
              <div className="text-red-600 text-4xl mb-2">❌</div>
              <p className="text-red-800 font-semibold mb-2">Map Loading Failed</p>
              <p className="text-red-700 text-sm">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Retry
              </button>
            </div>
          </div>
        );
      case 'success':
        return (
          <div className="relative">
            <div 
              ref={mapRef} 
              className="w-full h-64 rounded-lg border border-gray-300"
            />
            <div className="absolute top-2 left-2 bg-green-100 border border-green-300 px-3 py-1 rounded text-green-800 text-sm">
              ✅ Direct loading successful!
            </div>
          </div>
        );
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Direct Google Maps Test</h3>
        <div className="text-sm text-gray-600">
          Status: <span className={`font-medium ${
            status === 'success' ? 'text-green-600' : 
            status === 'error' ? 'text-red-600' : 'text-blue-600'
          }`}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
        </div>
      </div>
      
      {getStatusDisplay()}
      
      <div className="text-sm text-gray-600">
        <p><strong>What this tests:</strong></p>
        <ul className="list-disc list-inside mt-1 space-y-1">
          <li>Direct Google Maps JavaScript API loading</li>
          <li>API key validity and restrictions</li>
          <li>Basic map initialization</li>
          <li>Marker placement functionality</li>
        </ul>
      </div>
    </div>
  );
};

export default DirectMapTest;
