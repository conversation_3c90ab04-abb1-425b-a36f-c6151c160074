'use client';

import React, { useMemo } from 'react';
import InteractiveMap from './InteractiveMap';
import { TripRouteMapProps, TripData, MapMarker, MapPolyline } from '@/lib/maps/types';
import { TRIP_STATUS_COLORS } from '@/lib/maps/config';

/**
 * Trip Route Map Component
 * Specialized map for displaying trip routes with waypoints and progress
 */
const TripRouteMap: React.FC<TripRouteMapProps> = ({
  trip,
  showWaypoints = true,
  showProgress = true,
  showAlerts = true,
  interactive = true,
  showETA = true,
  routeColor,
  completedRouteColor,
  ...mapProps
}) => {
  // Create markers for origin, destination, and waypoints
  const tripMarkers: MapMarker[] = useMemo(() => {
    const markers: MapMarker[] = [];

    // Origin marker
    markers.push({
      id: `${trip.id}-origin`,
      position: trip.origin,
      title: 'Trip Origin',
      icon: {
        url: '/icons/marker-origin.png',
        scaledSize: new google.maps.Size(40, 40),
        anchor: new google.maps.Point(20, 40),
      },
      infoWindow: {
        content: (
          <div className="p-3">
            <h3 className="font-semibold text-green-900">Trip Origin</h3>
            <p className="text-sm text-gray-600 mt-1">{trip.name}</p>
            <p className="text-xs text-gray-500 mt-1">
              {trip.origin.lat.toFixed(6)}, {trip.origin.lng.toFixed(6)}
            </p>
          </div>
        ),
      },
    });

    // Destination marker
    markers.push({
      id: `${trip.id}-destination`,
      position: trip.destination,
      title: 'Trip Destination',
      icon: {
        url: '/icons/marker-destination.png',
        scaledSize: new google.maps.Size(40, 40),
        anchor: new google.maps.Point(20, 40),
      },
      infoWindow: {
        content: (
          <div className="p-3">
            <h3 className="font-semibold text-red-900">Trip Destination</h3>
            <p className="text-sm text-gray-600 mt-1">{trip.name}</p>
            <p className="text-xs text-gray-500 mt-1">
              {trip.destination.lat.toFixed(6)}, {trip.destination.lng.toFixed(6)}
            </p>
          </div>
        ),
      },
    });

    // Waypoint markers
    if (showWaypoints && trip.waypoints) {
      trip.waypoints.forEach((waypoint, index) => {
        markers.push({
          id: `${trip.id}-waypoint-${index}`,
          position: waypoint,
          title: `Waypoint ${index + 1}`,
          icon: {
            url: '/icons/marker-waypoint.png',
            scaledSize: new google.maps.Size(24, 24),
            anchor: new google.maps.Point(12, 24),
          },
          infoWindow: {
            content: (
              <div className="p-2">
                <h4 className="font-medium text-blue-900">Waypoint {index + 1}</h4>
                <p className="text-xs text-gray-500 mt-1">
                  {waypoint.lat.toFixed(6)}, {waypoint.lng.toFixed(6)}
                </p>
              </div>
            ),
          },
        });
      });
    }

    // Current position marker (for active trips)
    if (trip.currentPosition && trip.status === 'active') {
      markers.push({
        id: `${trip.id}-current`,
        position: trip.currentPosition,
        title: 'Current Position',
        icon: {
          url: '/icons/vehicle-current.png',
          scaledSize: new google.maps.Size(32, 32),
          anchor: new google.maps.Point(16, 16),
        },
        animation: google.maps.Animation.BOUNCE,
        infoWindow: {
          content: (
            <div className="p-3">
              <h3 className="font-semibold text-blue-900">Current Position</h3>
              {trip.vehicle && (
                <p className="text-sm text-gray-600 mt-1">
                  Vehicle: {trip.vehicle.name}
                </p>
              )}
              <p className="text-xs text-gray-500 mt-1">
                {trip.currentPosition.lat.toFixed(6)}, {trip.currentPosition.lng.toFixed(6)}
              </p>
              {showETA && trip.estimatedDuration && (
                <p className="text-xs text-blue-600 mt-1">
                  ETA: {trip.estimatedDuration} minutes
                </p>
              )}
            </div>
          ),
        },
      });
    }

    // Alert markers
    if (showAlerts && trip.alerts) {
      trip.alerts.forEach((alert, index) => {
        markers.push({
          id: `${trip.id}-alert-${index}`,
          position: alert.position,
          title: alert.message,
          icon: {
            url: getAlertIcon(alert.type),
            scaledSize: new google.maps.Size(24, 24),
            anchor: new google.maps.Point(12, 24),
          },
          infoWindow: {
            content: (
              <div className="p-3">
                <h4 className={`font-medium ${getAlertTextColor(alert.type)}`}>
                  {alert.type.toUpperCase()} Alert
                </h4>
                <p className="text-sm text-gray-700 mt-1">{alert.message}</p>
                <p className="text-xs text-gray-500 mt-2">
                  {alert.timestamp.toLocaleString()}
                </p>
              </div>
            ),
          },
        });
      });
    }

    return markers;
  }, [trip, showWaypoints, showAlerts, showETA]);

  // Create polylines for routes
  const routePolylines: MapPolyline[] = useMemo(() => {
    const polylines: MapPolyline[] = [];

    // Planned route
    if (trip.plannedRoute && trip.plannedRoute.length > 1) {
      polylines.push({
        id: `${trip.id}-planned-route`,
        path: trip.plannedRoute,
        strokeColor: routeColor || TRIP_STATUS_COLORS[trip.status],
        strokeOpacity: 0.6,
        strokeWeight: 4,
        geodesic: true,
        data: { type: 'planned', trip },
      });
    }

    // Completed route (for active/completed trips)
    if (showProgress && trip.completedRoute && trip.completedRoute.length > 1) {
      polylines.push({
        id: `${trip.id}-completed-route`,
        path: trip.completedRoute,
        strokeColor: completedRouteColor || '#4CAF50',
        strokeOpacity: 1.0,
        strokeWeight: 6,
        geodesic: true,
        data: { type: 'completed', trip },
      });
    }

    return polylines;
  }, [trip, showProgress, routeColor, completedRouteColor]);

  // Calculate map bounds to fit all trip elements
  const mapCenter = useMemo(() => {
    const positions = [trip.origin, trip.destination];
    
    if (trip.waypoints) {
      positions.push(...trip.waypoints);
    }
    
    if (trip.currentPosition) {
      positions.push(trip.currentPosition);
    }

    // Calculate center
    const totalLat = positions.reduce((sum, pos) => sum + pos.lat, 0);
    const totalLng = positions.reduce((sum, pos) => sum + pos.lng, 0);

    return {
      lat: totalLat / positions.length,
      lng: totalLng / positions.length,
    };
  }, [trip]);

  // Helper functions
  function getAlertIcon(type: 'warning' | 'danger' | 'info'): string {
    const iconMap = {
      warning: '/icons/alert-warning.png',
      danger: '/icons/alert-danger.png',
      info: '/icons/alert-info.png',
    };
    return iconMap[type];
  }

  function getAlertTextColor(type: 'warning' | 'danger' | 'info'): string {
    const colorMap = {
      warning: 'text-yellow-900',
      danger: 'text-red-900',
      info: 'text-blue-900',
    };
    return colorMap[type];
  }

  return (
    <div className="trip-route-map relative">
      <InteractiveMap
        {...mapProps}
        center={mapCenter}
        markers={tripMarkers}
        polylines={routePolylines}
        gestureHandling={interactive ? 'auto' : 'none'}
        showZoomControl={interactive}
        showMapTypeControl={interactive}
        showStreetViewControl={interactive}
        showFullscreenControl={interactive}
      />
      
      {/* Trip Info Panel */}
      <TripInfoPanel trip={trip} showETA={showETA} />
    </div>
  );
};

/**
 * Trip Info Panel Component
 */
interface TripInfoPanelProps {
  trip: TripData;
  showETA: boolean;
}

const TripInfoPanel: React.FC<TripInfoPanelProps> = ({ trip, showETA }) => {
  const getStatusBadgeClass = (status: TripData['status']): string => {
    const classes = {
      planned: 'bg-blue-100 text-blue-800',
      active: 'bg-green-100 text-green-800',
      completed: 'bg-gray-100 text-gray-800',
      cancelled: 'bg-red-100 text-red-800',
      delayed: 'bg-yellow-100 text-yellow-800',
    };
    return classes[status];
  };

  const calculateProgress = (): number => {
    if (!trip.completedRoute || !trip.plannedRoute) return 0;
    return Math.min((trip.completedRoute.length / trip.plannedRoute.length) * 100, 100);
  };

  return (
    <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-4 min-w-[280px] max-w-[320px]">
      <div className="flex items-center justify-between mb-3">
        <h3 className="font-semibold text-gray-900 truncate">{trip.name}</h3>
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(trip.status)}`}>
          {trip.status.toUpperCase()}
        </span>
      </div>
      
      <div className="space-y-2 text-sm">
        {trip.distance && (
          <div className="flex justify-between">
            <span className="text-gray-600">Distance:</span>
            <span className="font-medium">{trip.distance.toFixed(1)} km</span>
          </div>
        )}
        
        {trip.estimatedDuration && showETA && (
          <div className="flex justify-between">
            <span className="text-gray-600">Duration:</span>
            <span className="font-medium">{trip.estimatedDuration} min</span>
          </div>
        )}
        
        {trip.vehicle && (
          <div className="flex justify-between">
            <span className="text-gray-600">Vehicle:</span>
            <span className="font-medium truncate">{trip.vehicle.name}</span>
          </div>
        )}
        
        {trip.status === 'active' && (
          <div className="pt-2 border-t">
            <div className="flex justify-between mb-1">
              <span className="text-gray-600">Progress:</span>
              <span className="font-medium">{calculateProgress().toFixed(0)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${calculateProgress()}%` }}
              />
            </div>
          </div>
        )}
        
        {trip.alerts && trip.alerts.length > 0 && (
          <div className="pt-2 border-t">
            <div className="flex items-center text-yellow-600">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span className="text-xs font-medium">{trip.alerts.length} Alert(s)</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TripRouteMap;
