'use client';

import React, { useState, useEffect } from 'react';

interface ApiTestResult {
  status: 'loading' | 'success' | 'error';
  message: string;
  details?: any;
}

/**
 * API Key Tester Component
 * Tests Google Maps API key functionality
 */
const ApiKeyTester: React.FC = () => {
  const [testResult, setTestResult] = useState<ApiTestResult>({
    status: 'loading',
    message: 'Testing API key...'
  });

  useEffect(() => {
    testApiKey();
  }, []);

  const testApiKey = async () => {
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      setTestResult({
        status: 'error',
        message: 'API key not found in environment variables'
      });
      return;
    }

    try {
      // Test 1: Geocoding API (simpler test)
      const geocodeResponse = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=Riyadh,Saudi+Arabia&key=${apiKey}`
      );
      const geocodeData = await geocodeResponse.json();

      if (geocodeData.error_message) {
        setTestResult({
          status: 'error',
          message: `API Error: ${geocodeData.error_message}`,
          details: geocodeData
        });
        return;
      }

      if (geocodeData.status === 'REQUEST_DENIED') {
        setTestResult({
          status: 'error',
          message: 'Request denied - Check API key restrictions and enabled APIs',
          details: geocodeData
        });
        return;
      }

      if (geocodeData.status === 'OK') {
        setTestResult({
          status: 'success',
          message: 'API key is working correctly!',
          details: geocodeData
        });
        return;
      }

      // Test 2: Try loading Maps JavaScript API directly
      if (typeof window !== 'undefined' && !(window as any).google?.maps) {
        const script = document.createElement('script');
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,geometry,drawing`;
        script.async = true;
        script.defer = true;
        
        script.onload = () => {
          setTestResult({
            status: 'success',
            message: 'Maps JavaScript API loaded successfully!',
          });
        };
        
        script.onerror = () => {
          setTestResult({
            status: 'error',
            message: 'Failed to load Maps JavaScript API - Check API key and restrictions',
          });
        };
        
        document.head.appendChild(script);
      }

    } catch (error) {
      setTestResult({
        status: 'error',
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: error
      });
    }
  };

  const getStatusIcon = () => {
    switch (testResult.status) {
      case 'loading':
        return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>;
      case 'success':
        return <div className="text-green-600 text-xl">✅</div>;
      case 'error':
        return <div className="text-red-600 text-xl">❌</div>;
    }
  };

  const getStatusColor = () => {
    switch (testResult.status) {
      case 'loading':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
    }
  };

  return (
    <div className={`p-4 border rounded-lg ${getStatusColor()}`}>
      <div className="flex items-center gap-3 mb-3">
        {getStatusIcon()}
        <h3 className="font-semibold">API Key Test Result</h3>
      </div>
      
      <p className="text-sm mb-3">{testResult.message}</p>
      
      {testResult.status === 'error' && (
        <div className="space-y-3">
          <div className="text-sm">
            <strong>Common Solutions:</strong>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li>Check that "Maps JavaScript API" is enabled in Google Cloud Console</li>
              <li>Verify API key restrictions allow your domain: <code className="bg-white bg-opacity-50 px-1 rounded">{window.location.hostname}:3000/*</code></li>
              <li>Make sure billing is enabled for your Google Cloud project</li>
              <li>Try removing all API key restrictions temporarily for testing</li>
            </ul>
          </div>
          
          <button
            onClick={testApiKey}
            className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
          >
            Test Again
          </button>
        </div>
      )}
      
      {testResult.status === 'success' && (
        <div className="text-sm">
          <p>✅ Your API key is configured correctly!</p>
          <p>If maps still don't load, check browser console for JavaScript errors.</p>
        </div>
      )}
      
      {testResult.details && (
        <details className="mt-3">
          <summary className="cursor-pointer text-sm font-medium">Show Technical Details</summary>
          <pre className="mt-2 p-2 bg-white bg-opacity-50 rounded text-xs overflow-auto max-h-40">
            {JSON.stringify(testResult.details, null, 2)}
          </pre>
        </details>
      )}
    </div>
  );
};

export default ApiKeyTester;
