"use client";

import React, { useCallback, useRef, useState, useEffect } from "react";
import {
  GoogleMap,
  LoadScript,
  Marker,
  InfoWindow,
  Polyline,
  Polygon,
  Circle,
} from "@react-google-maps/api";
import { MarkerClusterer } from "@googlemaps/markerclusterer";
import {
  GOOGLE_MAPS_CONFIG,
  getMapOptions,
  MAP_STYLES,
} from "@/lib/maps/config";
import {
  InteractiveMapProps,
  MapMarker,
  MapLoadingState,
  MapPolyline,
  MapPolygon,
  MapCircle,
} from "@/lib/maps/types";

/**
 * Interactive Google Map Component
 * Base reusable map component with markers, clustering, and real-time updates
 */
const InteractiveMap: React.FC<InteractiveMapProps> = ({
  center,
  zoom,
  mapTypeId = "roadmap",
  width = "100%",
  height = "400px",
  className = "",
  markers = [],
  polylines = [],
  polygons = [],
  circles = [],
  onClick,
  onMarkerClick,
  onBoundsChanged,
  onZoomChanged,
  showTraffic = false,
  showTransit = false,
  showBicycling = false,
  gestureHandling = "auto",
  enableClustering = false,
  clusterOptions,
  realTimeUpdates = false,
  updateInterval = 5000,
  showZoomControl = true,
  showMapTypeControl = true,
  showStreetViewControl = true,
  showFullscreenControl = true,
  mapStyles,
  theme = "default",
  loading = false,
  error,
  onLoadError,
}) => {
  // State management
  const [loadingState, setLoadingState] = useState<MapLoadingState>({
    isLoading: true,
    isLoaded: false,
    error: null,
  });

  const [selectedMarker, setSelectedMarker] = useState<MapMarker | null>(null);
  const [mapInstance, setMapInstance] = useState<google.maps.Map | null>(null);
  const [clusterer, setClusterer] = useState<MarkerClusterer | null>(null);

  // Refs
  const mapRef = useRef<google.maps.Map | null>(null);
  const markersRef = useRef<google.maps.Marker[]>([]);
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Map options with theme support
  const mapOptions = getMapOptions({
    mapTypeId,
    gestureHandling,
    zoomControl: showZoomControl,
    mapTypeControl: showMapTypeControl,
    streetViewControl: showStreetViewControl,
    fullscreenControl: showFullscreenControl,
    styles: mapStyles || MAP_STYLES[theme],
  });

  // Handle map load
  const handleMapLoad = useCallback(
    (map: google.maps.Map) => {
      mapRef.current = map;
      setMapInstance(map);

      setLoadingState({
        isLoading: false,
        isLoaded: true,
        error: null,
      });

      // Initialize marker clusterer if enabled
      if (enableClustering) {
        const clustererInstance = new MarkerClusterer({
          map,
          markers: [],
          ...GOOGLE_MAPS_CONFIG.clusterOptions,
          ...clusterOptions,
        });
        setClusterer(clustererInstance);
      }
    },
    [enableClustering, clusterOptions]
  );

  // Handle map click
  const handleMapClick = useCallback(
    (event: google.maps.MapMouseEvent) => {
      setSelectedMarker(null);
      if (onClick && event.latLng) {
        onClick(event);
      }
    },
    [onClick]
  );

  // Handle marker click
  const handleMarkerClick = useCallback(
    (marker: MapMarker) => {
      setSelectedMarker(marker);
      if (onMarkerClick) {
        onMarkerClick(marker);
      }
    },
    [onMarkerClick]
  );

  // Handle bounds changed
  const handleBoundsChanged = useCallback(() => {
    if (mapRef.current && onBoundsChanged) {
      const bounds = mapRef.current.getBounds();
      if (bounds) {
        onBoundsChanged(bounds);
      }
    }
  }, [onBoundsChanged]);

  // Handle zoom changed
  const handleZoomChanged = useCallback(() => {
    if (mapRef.current && onZoomChanged) {
      const currentZoom = mapRef.current.getZoom();
      if (currentZoom !== undefined) {
        onZoomChanged(currentZoom);
      }
    }
  }, [onZoomChanged]);

  // Update markers when markers prop changes
  useEffect(() => {
    if (!mapInstance) return;

    // Clear existing markers
    markersRef.current.forEach((marker) => marker.setMap(null));
    markersRef.current = [];

    if (clusterer) {
      clusterer.clearMarkers();
    }

    // Create new markers
    const newMarkers = markers.map((markerData) => {
      const marker = new google.maps.Marker({
        position: markerData.position,
        map: enableClustering ? null : mapInstance,
        title: markerData.title,
        icon: markerData.icon,
        animation: markerData.animation,
        draggable: markerData.draggable,
        zIndex: markerData.zIndex,
        opacity: markerData.opacity,
      });

      // Add click listener
      marker.addListener("click", () => handleMarkerClick(markerData));

      return marker;
    });

    markersRef.current = newMarkers;

    // Add markers to clusterer if clustering is enabled
    if (clusterer && enableClustering) {
      clusterer.addMarkers(newMarkers);
    }
  }, [markers, mapInstance, clusterer, enableClustering, handleMarkerClick]);

  // Real-time updates
  useEffect(() => {
    if (!realTimeUpdates || !updateInterval) return;

    updateIntervalRef.current = setInterval(() => {
      // Trigger re-render or fetch new data
      // This would typically call a callback to fetch fresh data
      console.log("Real-time update triggered");
    }, updateInterval);

    return () => {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, [realTimeUpdates, updateInterval]);

  // Handle load script error
  const handleLoadError = useCallback(
    (error: Error) => {
      setLoadingState({
        isLoading: false,
        isLoaded: false,
        error: error.message,
      });

      if (onLoadError) {
        onLoadError(error);
      }
    },
    [onLoadError]
  );

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }

      if (clusterer) {
        clusterer.clearMarkers();
      }

      markersRef.current.forEach((marker) => marker.setMap(null));
    };
  }, [clusterer]);

  // Loading state
  if (loading || loadingState.isLoading) {
    return (
      <div className={`map-loading ${className}`} style={{ width, height }}>
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Loading map...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error || loadingState.error) {
    return (
      <div className={`map-error ${className}`} style={{ width, height }}>
        <div className="flex flex-col items-center justify-center h-full text-red-600">
          <div className="text-lg font-semibold mb-2">Map Load Error</div>
          <div className="text-sm text-center mb-4">
            {error || loadingState.error || "Failed to load map"}
          </div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`interactive-map ${className}`} style={{ width, height }}>
      <LoadScript
        googleMapsApiKey={GOOGLE_MAPS_CONFIG.apiKey}
        libraries={["places", "geometry", "drawing"]}
        region={GOOGLE_MAPS_CONFIG.region}
        language={GOOGLE_MAPS_CONFIG.language}
        onError={handleLoadError}
      >
        <GoogleMap
          mapContainerStyle={{ width: "100%", height: "100%" }}
          center={center}
          zoom={zoom}
          options={mapOptions}
          onLoad={handleMapLoad}
          onClick={handleMapClick}
          onBoundsChanged={handleBoundsChanged}
          onZoomChanged={handleZoomChanged}
        >
          {/* Traffic Layer */}
          {showTraffic && <div>{/* Traffic layer would be added here */}</div>}

          {/* Transit Layer */}
          {showTransit && <div>{/* Transit layer would be added here */}</div>}

          {/* Bicycling Layer */}
          {showBicycling && (
            <div>{/* Bicycling layer would be added here */}</div>
          )}

          {/* Polylines */}
          {polylines.map((polyline) => (
            <Polyline
              key={polyline.id}
              path={polyline.path}
              options={{
                strokeColor: polyline.strokeColor || "#FF0000",
                strokeOpacity: polyline.strokeOpacity || 1.0,
                strokeWeight: polyline.strokeWeight || 2,
                geodesic: polyline.geodesic || false,
                editable: polyline.editable || false,
                draggable: polyline.draggable || false,
              }}
              onClick={() => polyline.onClick?.(polyline)}
            />
          ))}

          {/* Polygons */}
          {polygons.map((polygon) => (
            <Polygon
              key={polygon.id}
              paths={polygon.paths}
              options={{
                strokeColor: polygon.strokeColor || "#FF0000",
                strokeOpacity: polygon.strokeOpacity || 0.8,
                strokeWeight: polygon.strokeWeight || 2,
                fillColor: polygon.fillColor || "#FF0000",
                fillOpacity: polygon.fillOpacity || 0.35,
                editable: polygon.editable || false,
                draggable: polygon.draggable || false,
              }}
              onClick={() => polygon.onClick?.(polygon)}
            />
          ))}

          {/* Circles */}
          {circles.map((circle) => (
            <Circle
              key={circle.id}
              center={circle.center}
              radius={circle.radius}
              options={{
                strokeColor: circle.strokeColor || "#FF0000",
                strokeOpacity: circle.strokeOpacity || 0.8,
                strokeWeight: circle.strokeWeight || 2,
                fillColor: circle.fillColor || "#FF0000",
                fillOpacity: circle.fillOpacity || 0.35,
                editable: circle.editable || false,
                draggable: circle.draggable || false,
              }}
              onClick={() => circle.onClick?.(circle)}
            />
          ))}

          {/* Info Window for selected marker */}
          {selectedMarker && selectedMarker.infoWindow && (
            <InfoWindow
              position={selectedMarker.position}
              onCloseClick={() => {
                setSelectedMarker(null);
                if (selectedMarker.infoWindow?.onClose) {
                  selectedMarker.infoWindow.onClose();
                }
              }}
            >
              <div className="info-window-content">
                {selectedMarker.infoWindow.content}
              </div>
            </InfoWindow>
          )}
        </GoogleMap>
      </LoadScript>
    </div>
  );
};

export default InteractiveMap;
