'use client';

import React, { useState, useEffect, useMemo } from 'react';
import InteractiveMap from './InteractiveMap';
import { VehicleTrackingMapProps, Vehicle, MapMarker, MapPolyline } from '@/lib/maps/types';
import { VEHICLE_STATUS_COLORS } from '@/lib/maps/config';

/**
 * Vehicle Tracking Map Component
 * Specialized map for tracking vehicles with real-time updates
 */
const VehicleTrackingMap: React.FC<VehicleTrackingMapProps> = ({
  vehicles,
  showRoutes = true,
  showGeofences = false,
  selectedVehicle,
  onVehicleSelect,
  trackingMode = 'live',
  timeRange,
  showVehicleInfo = true,
  vehicleIconSize = 32,
  ...mapProps
}) => {
  const [hoveredVehicle, setHoveredVehicle] = useState<string | null>(null);

  // Convert vehicles to map markers
  const vehicleMarkers: MapMarker[] = useMemo(() => {
    return vehicles.map((vehicle) => ({
      id: vehicle.id,
      position: vehicle.position,
      title: vehicle.name,
      icon: {
        url: getVehicleIcon(vehicle.status),
        scaledSize: new google.maps.Size(vehicleIconSize, vehicleIconSize),
        anchor: new google.maps.Point(vehicleIconSize / 2, vehicleIconSize / 2),
      },
      infoWindow: showVehicleInfo ? {
        content: (
          <VehicleInfoWindow 
            vehicle={vehicle} 
            onSelect={() => onVehicleSelect?.(vehicle)}
          />
        ),
      } : undefined,
      onClick: (marker) => {
        const vehicle = vehicles.find(v => v.id === marker.id);
        if (vehicle && onVehicleSelect) {
          onVehicleSelect(vehicle);
        }
      },
      data: vehicle,
    }));
  }, [vehicles, vehicleIconSize, showVehicleInfo, onVehicleSelect]);

  // Convert vehicle routes to polylines
  const routePolylines: MapPolyline[] = useMemo(() => {
    if (!showRoutes) return [];

    return vehicles
      .filter(vehicle => vehicle.route && vehicle.route.length > 1)
      .map((vehicle) => ({
        id: `route-${vehicle.id}`,
        path: vehicle.route!,
        strokeColor: VEHICLE_STATUS_COLORS[vehicle.status],
        strokeOpacity: selectedVehicle === vehicle.id ? 1.0 : 0.6,
        strokeWeight: selectedVehicle === vehicle.id ? 4 : 2,
        geodesic: true,
        onClick: (polyline) => {
          const vehicle = vehicles.find(v => `route-${v.id}` === polyline.id);
          if (vehicle && onVehicleSelect) {
            onVehicleSelect(vehicle);
          }
        },
        data: vehicle,
      }));
  }, [vehicles, showRoutes, selectedVehicle, onVehicleSelect]);

  // Handle marker click
  const handleMarkerClick = (marker: MapMarker) => {
    const vehicle = marker.data as Vehicle;
    if (onVehicleSelect) {
      onVehicleSelect(vehicle);
    }
  };

  // Get vehicle icon based on status
  function getVehicleIcon(status: Vehicle['status']): string {
    const iconMap = {
      online: '/icons/vehicle-online.png',
      offline: '/icons/vehicle-offline.png',
      idle: '/icons/vehicle-idle.png',
      moving: '/icons/vehicle-moving.png',
      maintenance: '/icons/vehicle-maintenance.png',
    };
    
    return iconMap[status] || iconMap.online;
  }

  // Calculate map center based on vehicles
  const mapCenter = useMemo(() => {
    if (selectedVehicle) {
      const vehicle = vehicles.find(v => v.id === selectedVehicle);
      if (vehicle) {
        return vehicle.position;
      }
    }

    if (vehicles.length === 0) {
      return mapProps.center;
    }

    // Calculate center of all vehicles
    const totalLat = vehicles.reduce((sum, vehicle) => sum + vehicle.position.lat, 0);
    const totalLng = vehicles.reduce((sum, vehicle) => sum + vehicle.position.lng, 0);

    return {
      lat: totalLat / vehicles.length,
      lng: totalLng / vehicles.length,
    };
  }, [vehicles, selectedVehicle, mapProps.center]);

  return (
    <div className="vehicle-tracking-map">
      <InteractiveMap
        {...mapProps}
        center={mapCenter}
        markers={vehicleMarkers}
        polylines={routePolylines}
        onMarkerClick={handleMarkerClick}
        enableClustering={vehicles.length > 20}
        realTimeUpdates={trackingMode === 'live'}
        updateInterval={5000} // Update every 5 seconds for live tracking
      />
      
      {/* Vehicle Status Legend */}
      <VehicleStatusLegend vehicles={vehicles} />
    </div>
  );
};

/**
 * Vehicle Info Window Component
 */
interface VehicleInfoWindowProps {
  vehicle: Vehicle;
  onSelect?: () => void;
}

const VehicleInfoWindow: React.FC<VehicleInfoWindowProps> = ({ vehicle, onSelect }) => {
  const formatLastUpdate = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins} min ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className="vehicle-info-window p-3 min-w-[250px]">
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-semibold text-lg text-gray-900">{vehicle.name}</h3>
        <span 
          className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(vehicle.status)}`}
        >
          {vehicle.status.toUpperCase()}
        </span>
      </div>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">Speed:</span>
          <span className="font-medium">{vehicle.speed} km/h</span>
        </div>
        
        <div className="flex justify-between">
          <span className="text-gray-600">Heading:</span>
          <span className="font-medium">{vehicle.heading}°</span>
        </div>
        
        {vehicle.driver && (
          <div className="flex justify-between">
            <span className="text-gray-600">Driver:</span>
            <span className="font-medium">{vehicle.driver.name}</span>
          </div>
        )}
        
        <div className="flex justify-between">
          <span className="text-gray-600">Last Update:</span>
          <span className="font-medium">{formatLastUpdate(vehicle.lastUpdate)}</span>
        </div>
        
        <div className="pt-2 border-t">
          <div className="text-xs text-gray-500">
            Lat: {vehicle.position.lat.toFixed(6)}<br />
            Lng: {vehicle.position.lng.toFixed(6)}
          </div>
        </div>
      </div>
      
      {onSelect && (
        <button
          onClick={onSelect}
          className="mt-3 w-full px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          View Details
        </button>
      )}
    </div>
  );
};

/**
 * Vehicle Status Legend Component
 */
interface VehicleStatusLegendProps {
  vehicles: Vehicle[];
}

const VehicleStatusLegend: React.FC<VehicleStatusLegendProps> = ({ vehicles }) => {
  const statusCounts = useMemo(() => {
    const counts = {
      online: 0,
      offline: 0,
      idle: 0,
      moving: 0,
      maintenance: 0,
    };
    
    vehicles.forEach(vehicle => {
      counts[vehicle.status]++;
    });
    
    return counts;
  }, [vehicles]);

  return (
    <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-4 min-w-[200px]">
      <h4 className="font-semibold text-gray-900 mb-3">Vehicle Status</h4>
      <div className="space-y-2">
        {Object.entries(statusCounts).map(([status, count]) => (
          <div key={status} className="flex items-center justify-between">
            <div className="flex items-center">
              <div 
                className="w-3 h-3 rounded-full mr-2"
                style={{ backgroundColor: VEHICLE_STATUS_COLORS[status as keyof typeof VEHICLE_STATUS_COLORS] }}
              />
              <span className="text-sm capitalize">{status}</span>
            </div>
            <span className="text-sm font-medium">{count}</span>
          </div>
        ))}
      </div>
      <div className="pt-2 mt-2 border-t">
        <div className="flex justify-between">
          <span className="text-sm font-medium">Total:</span>
          <span className="text-sm font-medium">{vehicles.length}</span>
        </div>
      </div>
    </div>
  );
};

// Helper function for status badge classes
function getStatusBadgeClass(status: Vehicle['status']): string {
  const classes = {
    online: 'bg-green-100 text-green-800',
    offline: 'bg-red-100 text-red-800',
    idle: 'bg-yellow-100 text-yellow-800',
    moving: 'bg-blue-100 text-blue-800',
    maintenance: 'bg-purple-100 text-purple-800',
  };
  
  return classes[status] || classes.online;
}

export default VehicleTrackingMap;
