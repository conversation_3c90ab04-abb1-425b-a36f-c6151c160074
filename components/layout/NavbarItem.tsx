'use client';

import Link from 'next/link';
import { ChevronDown } from 'lucide-react';
import { Button } from '../ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { useLanguage } from '../../contexts/LanguageContext';

interface NavigationItem {
  key: string;
  href: string;
  hasDropdown?: boolean;
}

interface NavbarItemProps {
  item: NavigationItem;
  isActive: boolean;
  className?: string;
}

export default function NavbarItem({ item, isActive, className = '' }: NavbarItemProps) {
  const { t, dir } = useLanguage();

  if (item.hasDropdown) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className={`flex items-center space-x-1 rtl:space-x-reverse px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              isActive
                ? 'bg-[#007bff] text-white hover:bg-[#0056b3]'
                : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
            } ${className}`}
          >
            <span>{t(`navigation.${item.key}`)}</span>
            <ChevronDown className="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          align={dir === 'rtl' ? 'start' : 'end'} 
          className="w-56"
        >
          <DropdownMenuItem>
            <Link href="/reports" className="w-full">
              Operational Reports
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Link href="/reports" className="w-full">
              Compliance Reports
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Link href="/reports" className="w-full">
              Security Reports
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <Link href={item.href}>
      <Button
        variant="ghost"
        className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
          isActive
            ? 'bg-[#007bff] text-white hover:bg-[#0056b3]'
            : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
        } ${className}`}
      >
        {t(`navigation.${item.key}`)}
      </Button>
    </Link>
  );
}
