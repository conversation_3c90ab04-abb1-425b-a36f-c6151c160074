/**
 * Google Maps Integration Types
 * TypeScript interfaces and types for map components
 */

import { MarkerClustererOptions } from '@googlemaps/markerclusterer';

// Base map interfaces
export interface MapPosition {
  lat: number;
  lng: number;
}

export interface MapBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

// Core map component props
export interface InteractiveMapProps {
  // Map Configuration
  center: MapPosition;
  zoom: number;
  mapTypeId?: google.maps.MapTypeId;
  
  // Dimensions
  width?: string | number;
  height?: string | number;
  className?: string;
  
  // Markers & Overlays
  markers?: MapMarker[];
  polylines?: MapPolyline[];
  polygons?: MapPolygon[];
  circles?: MapCircle[];
  
  // Interaction
  onClick?: (event: google.maps.MapMouseEvent) => void;
  onMarkerClick?: (marker: MapMarker) => void;
  onBoundsChanged?: (bounds: google.maps.LatLngBounds) => void;
  onZoomChanged?: (zoom: number) => void;
  
  // Features
  showTraffic?: boolean;
  showTransit?: boolean;
  showBicycling?: boolean;
  gestureHandling?: 'cooperative' | 'greedy' | 'none' | 'auto';
  
  // Clustering
  enableClustering?: boolean;
  clusterOptions?: MarkerClustererOptions;
  
  // Real-time
  realTimeUpdates?: boolean;
  updateInterval?: number;
  
  // Controls
  showZoomControl?: boolean;
  showMapTypeControl?: boolean;
  showStreetViewControl?: boolean;
  showFullscreenControl?: boolean;
  
  // Styling
  mapStyles?: google.maps.MapTypeStyle[];
  theme?: 'default' | 'dark' | 'minimal';
  
  // Loading & Error
  loading?: boolean;
  error?: string;
  onLoadError?: (error: Error) => void;
}

// Marker interface
export interface MapMarker {
  id: string;
  position: MapPosition;
  title?: string;
  icon?: string | google.maps.Icon | google.maps.Symbol;
  infoWindow?: {
    content: React.ReactNode;
    onClose?: () => void;
  };
  animation?: google.maps.Animation;
  draggable?: boolean;
  onClick?: (marker: MapMarker) => void;
  data?: any; // Custom data for the marker
  zIndex?: number;
  opacity?: number;
}

// Polyline interface
export interface MapPolyline {
  id: string;
  path: MapPosition[];
  strokeColor?: string;
  strokeOpacity?: number;
  strokeWeight?: number;
  geodesic?: boolean;
  editable?: boolean;
  draggable?: boolean;
  onClick?: (polyline: MapPolyline) => void;
  data?: any;
}

// Polygon interface
export interface MapPolygon {
  id: string;
  paths: MapPosition[] | MapPosition[][];
  strokeColor?: string;
  strokeOpacity?: number;
  strokeWeight?: number;
  fillColor?: string;
  fillOpacity?: number;
  editable?: boolean;
  draggable?: boolean;
  onClick?: (polygon: MapPolygon) => void;
  data?: any;
}

// Circle interface
export interface MapCircle {
  id: string;
  center: MapPosition;
  radius: number; // in meters
  strokeColor?: string;
  strokeOpacity?: number;
  strokeWeight?: number;
  fillColor?: string;
  fillOpacity?: number;
  editable?: boolean;
  draggable?: boolean;
  onClick?: (circle: MapCircle) => void;
  data?: any;
}

// Vehicle tracking specific types
export interface Vehicle {
  id: string;
  name: string;
  position: MapPosition;
  heading: number; // degrees
  speed: number; // km/h
  status: 'online' | 'offline' | 'idle' | 'moving' | 'maintenance';
  lastUpdate: Date;
  route?: MapPosition[];
  driver?: {
    name: string;
    id: string;
    phone?: string;
  };
  vehicleInfo?: {
    make: string;
    model: string;
    year: number;
    licensePlate: string;
    color: string;
  };
}

export interface VehicleTrackingMapProps extends InteractiveMapProps {
  vehicles: Vehicle[];
  showRoutes?: boolean;
  showGeofences?: boolean;
  selectedVehicle?: string;
  onVehicleSelect?: (vehicle: Vehicle) => void;
  trackingMode?: 'live' | 'historical';
  timeRange?: {
    start: Date;
    end: Date;
  };
  showVehicleInfo?: boolean;
  vehicleIconSize?: number;
}

// Trip route specific types
export interface TripAlert {
  id: string;
  type: 'warning' | 'danger' | 'info';
  message: string;
  position: MapPosition;
  timestamp: Date;
}

export interface TripData {
  id: string;
  name: string;
  origin: MapPosition;
  destination: MapPosition;
  waypoints: MapPosition[];
  currentPosition?: MapPosition;
  completedRoute?: MapPosition[];
  plannedRoute?: MapPosition[];
  alerts?: TripAlert[];
  status: 'planned' | 'active' | 'completed' | 'cancelled' | 'delayed';
  estimatedDuration?: number; // minutes
  actualDuration?: number; // minutes
  distance?: number; // kilometers
  vehicle?: Vehicle;
}

export interface TripRouteMapProps extends InteractiveMapProps {
  trip: TripData;
  showWaypoints?: boolean;
  showProgress?: boolean;
  showAlerts?: boolean;
  interactive?: boolean;
  showETA?: boolean;
  routeColor?: string;
  completedRouteColor?: string;
}

// Geofence management types
export interface Geofence {
  id: string;
  name: string;
  description?: string;
  type: 'circle' | 'polygon';
  coordinates: MapPosition[];
  radius?: number; // for circle type, in meters
  color: string;
  strokeColor: string;
  fillOpacity: number;
  strokeWeight: number;
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
  rules?: {
    alertOnEntry?: boolean;
    alertOnExit?: boolean;
    allowedVehicles?: string[];
    timeRestrictions?: {
      startTime: string;
      endTime: string;
      days: number[]; // 0-6, Sunday to Saturday
    };
  };
}

export interface GeofenceMapProps extends InteractiveMapProps {
  geofences: Geofence[];
  editMode?: boolean;
  selectedGeofence?: string;
  onGeofenceCreate?: (geofence: Omit<Geofence, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onGeofenceEdit?: (geofence: Geofence) => void;
  onGeofenceDelete?: (geofenceId: string) => void;
  onGeofenceSelect?: (geofence: Geofence | null) => void;
  drawingMode?: 'circle' | 'polygon' | null;
  showGeofenceInfo?: boolean;
}

// Dashboard overview map types
export interface DashboardMapData {
  vehicles: Vehicle[];
  activeTrips: TripData[];
  geofences: Geofence[];
  alerts: TripAlert[];
}

export interface DashboardOverviewMapProps extends InteractiveMapProps {
  data: DashboardMapData;
  showVehicles?: boolean;
  showTrips?: boolean;
  showGeofences?: boolean;
  showAlerts?: boolean;
  onVehicleClick?: (vehicle: Vehicle) => void;
  onTripClick?: (trip: TripData) => void;
  onAlertClick?: (alert: TripAlert) => void;
  compactMode?: boolean;
}

// Map utilities types
export interface MapUtils {
  calculateDistance: (point1: MapPosition, point2: MapPosition) => number;
  calculateBounds: (positions: MapPosition[]) => MapBounds;
  isPointInPolygon: (point: MapPosition, polygon: MapPosition[]) => boolean;
  formatCoordinates: (position: MapPosition) => string;
  geocodeAddress: (address: string) => Promise<MapPosition | null>;
  reverseGeocode: (position: MapPosition) => Promise<string | null>;
}

// Map loading states
export interface MapLoadingState {
  isLoading: boolean;
  isLoaded: boolean;
  error: string | null;
  progress?: number;
}

// Map event types
export type MapEventType = 
  | 'click'
  | 'dblclick'
  | 'rightclick'
  | 'mouseover'
  | 'mouseout'
  | 'mousemove'
  | 'drag'
  | 'dragend'
  | 'zoom_changed'
  | 'bounds_changed'
  | 'center_changed'
  | 'idle';

export interface MapEvent {
  type: MapEventType;
  position?: MapPosition;
  target?: any;
  data?: any;
}

// Export all types for easy importing
export type {
  MarkerClustererOptions,
};
