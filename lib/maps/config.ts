/**
 * Google Maps Configuration
 * Centralized configuration for Google Maps integration
 */

export const GOOGLE_MAPS_CONFIG = {
  // API Key from environment variables
  apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "",

  // Required libraries for Google Maps
  libraries: ["places", "geometry", "drawing"],

  // Regional settings for Saudi Arabia
  region: "SA",
  language: "ar",

  // Default map settings
  defaultCenter: {
    lat: 24.7136, // Riyadh coordinates
    lng: 46.6753,
  },

  // Default zoom level
  defaultZoom: 10,

  // Map styling options
  mapOptions: {
    disableDefaultUI: false,
    zoomControl: true,
    mapTypeControl: true,
    scaleControl: true,
    streetViewControl: true,
    rotateControl: true,
    fullscreenControl: true,
    gestureHandling: "auto",

    // Map types available
    mapTypeId: "roadmap",

    // Styling for better UX
    styles: [
      {
        featureType: "poi",
        elementType: "labels",
        stylers: [{ visibility: "off" }],
      },
    ] as google.maps.MapTypeStyle[],
  },

  // Marker clustering options
  clusterOptions: {
    minimumClusterSize: 2,
    maxZoom: 15,
    gridSize: 60,
    styles: [
      {
        textColor: "white",
        url: "/icons/cluster-icon.png",
        height: 40,
        width: 40,
        textSize: 12,
      },
    ],
  },

  // Performance settings
  performance: {
    // Debounce time for real-time updates (ms)
    updateDebounceTime: 1000,

    // Maximum number of markers before clustering
    maxMarkersBeforeClustering: 100,

    // Viewport padding for bounds calculation
    viewportPadding: 50,
  },

  // Error messages
  errorMessages: {
    apiKeyMissing:
      "Google Maps API key is missing. Please check your environment variables.",
    loadFailed:
      "Failed to load Google Maps. Please check your internet connection.",
    geocodeFailed: "Failed to geocode address. Please try again.",
  },
} as const;

/**
 * Validate Google Maps configuration
 */
export const validateMapsConfig = (): boolean => {
  if (!GOOGLE_MAPS_CONFIG.apiKey) {
    console.error(GOOGLE_MAPS_CONFIG.errorMessages.apiKeyMissing);
    return false;
  }

  return true;
};

/**
 * Get map options with custom overrides
 */
export const getMapOptions = (
  overrides?: Partial<google.maps.MapOptions>
): google.maps.MapOptions => {
  return {
    ...GOOGLE_MAPS_CONFIG.mapOptions,
    ...overrides,
  };
};

/**
 * Common map styles for different themes
 */
export const MAP_STYLES = {
  default: [] as google.maps.MapTypeStyle[],

  // Dark theme for night mode
  dark: [
    { elementType: "geometry", stylers: [{ color: "#242f3e" }] },
    { elementType: "labels.text.stroke", stylers: [{ color: "#242f3e" }] },
    { elementType: "labels.text.fill", stylers: [{ color: "#746855" }] },
    {
      featureType: "administrative.locality",
      elementType: "labels.text.fill",
      stylers: [{ color: "#d59563" }],
    },
    {
      featureType: "poi",
      elementType: "labels.text.fill",
      stylers: [{ color: "#d59563" }],
    },
    {
      featureType: "poi.park",
      elementType: "geometry",
      stylers: [{ color: "#263c3f" }],
    },
    {
      featureType: "poi.park",
      elementType: "labels.text.fill",
      stylers: [{ color: "#6b9a76" }],
    },
    {
      featureType: "road",
      elementType: "geometry",
      stylers: [{ color: "#38414e" }],
    },
    {
      featureType: "road",
      elementType: "geometry.stroke",
      stylers: [{ color: "#212a37" }],
    },
    {
      featureType: "road",
      elementType: "labels.text.fill",
      stylers: [{ color: "#9ca5b3" }],
    },
    {
      featureType: "road.highway",
      elementType: "geometry",
      stylers: [{ color: "#746855" }],
    },
    {
      featureType: "road.highway",
      elementType: "geometry.stroke",
      stylers: [{ color: "#1f2835" }],
    },
    {
      featureType: "road.highway",
      elementType: "labels.text.fill",
      stylers: [{ color: "#f3d19c" }],
    },
    {
      featureType: "transit",
      elementType: "geometry",
      stylers: [{ color: "#2f3948" }],
    },
    {
      featureType: "transit.station",
      elementType: "labels.text.fill",
      stylers: [{ color: "#d59563" }],
    },
    {
      featureType: "water",
      elementType: "geometry",
      stylers: [{ color: "#17263c" }],
    },
    {
      featureType: "water",
      elementType: "labels.text.fill",
      stylers: [{ color: "#515c6d" }],
    },
    {
      featureType: "water",
      elementType: "labels.text.stroke",
      stylers: [{ color: "#17263c" }],
    },
  ],

  // Minimal style for cleaner look
  minimal: [
    {
      featureType: "poi",
      elementType: "labels",
      stylers: [{ visibility: "off" }],
    },
    {
      featureType: "transit",
      elementType: "labels",
      stylers: [{ visibility: "off" }],
    },
  ],
} as Record<string, google.maps.MapTypeStyle[]>;

/**
 * Vehicle status colors for markers
 */
export const VEHICLE_STATUS_COLORS = {
  online: "#4CAF50", // Green
  offline: "#F44336", // Red
  idle: "#FF9800", // Orange
  moving: "#2196F3", // Blue
  maintenance: "#9C27B0", // Purple
} as const;

/**
 * Trip status colors for routes
 */
export const TRIP_STATUS_COLORS = {
  planned: "#2196F3", // Blue
  active: "#4CAF50", // Green
  completed: "#9E9E9E", // Gray
  cancelled: "#F44336", // Red
  delayed: "#FF9800", // Orange
} as const;
