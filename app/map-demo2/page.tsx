'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { <PERSON><PERSON>hart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

export default function MapDemo2() {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);

  // UI state for sidebar
  const [activeTab, setActiveTab] = useState('search');
  const [selectedFilters, setSelectedFilters] = useState({
    showPort: true,
    showCheckpost: true,
    tripCode: 'Trip Code',
    searchValue: ''
  });

  // Routes tab state
  const [routeFilter, setRouteFilter] = useState('my-routes'); // 'my-routes', 'all'
  const [routeName, setRouteName] = useState('');
  const [selectedRoutes, setSelectedRoutes] = useState<string[]>([]);

  // User's saved routes (for "My Routes" button)
  const myRoutes = [
    'Jeddah Islamic Seaport-King Fahad Intl Airport',
    'King Fahad Intl Airport -Khafji Border',
    'Batha Customs Border -Jeddah Islamic Seaport'
  ];

  // Trip statistics (mock data)
  const pieData = [
    { name: 'Active Trips With Alerts', value: 11, color: '#3b82f6' },
    { name: 'Active Trips Without Alerts', value: 1551, color: '#f97316' }
  ];

  const barData = [
    {
      name: '0',
      'Active Trips': 1562,
      'Active Trips With Alerts': 11,
      'Active Trips With Communication Lost': 1
    }
  ];

  // Route autocomplete options
  const routeOptions = [
    'Jeddah Islamic Seaport-King Fahad Intl Airport',
    'Jeddah Islamic Seaport-Batha Customs Border',
    'King Fahad Intl Airport -Khafji Border',
    'Batha Customs Border -Jeddah Islamic Seaport',
    'Ruqaie Border-Diba Seaport Customs',
    'Batha Customs Border -Haditha Customs Border',
    'test',
    'Riyadh Port-Dammam Seaport',
    'Al Wadiah Border-Jizan Port',
    'Salwa Border-Jubail Port',
    'Durra Border-Yanbu Port'
  ];

  const [filteredRoutes, setFilteredRoutes] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Mock alerts data
  const alertsData = [
    { id: 1, title: 'Speed Alert', description: 'Vehicle exceeded speed limit', time: '10 mins ago', severity: 'high' },
    { id: 2, title: 'Geofence Alert', description: 'Vehicle left designated area', time: '25 mins ago', severity: 'medium' },
    { id: 3, title: 'E-lock Alert', description: 'E-lock tampering detected', time: '1 hour ago', severity: 'high' },
    { id: 4, title: 'Delay Alert', description: 'Vehicle behind schedule', time: '2 hours ago', severity: 'low' },
    { id: 5, title: 'Stop Alert', description: 'Unscheduled stop detected', time: '3 hours ago', severity: 'medium' }
  ];

  // Sample locations data
  const sampleLocations = [
    { id: 1, name: 'Riyadh Airport', lat: 24.9576, lng: 46.6988, type: 'airport' },
    { id: 2, name: 'Jeddah Airport', lat: 21.6796, lng: 39.1564, type: 'airport' },
    { id: 3, name: 'Dammam Airport', lat: 26.4712, lng: 49.7979, type: 'airport' },
    { id: 4, name: 'King Fahd Causeway', lat: 26.2361, lng: 50.1500, type: 'landport' },
    { id: 5, name: 'Riyadh Police Station', lat: 24.7136, lng: 46.6753, type: 'police_station' },
    { id: 6, name: 'Border Checkpoint', lat: 25.0000, lng: 47.0000, type: 'checkpoint' }
  ];

  // Create SVG marker icons
  const createSVGMarker = (type: string) => {
    const iconConfigs = {
      airport: {
        color: '#10b981',
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z"/>
        </svg>`
      },
      landport: {
        color: '#3b82f6',
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
        </svg>`
      },
      police_station: {
        color: '#ef4444',
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
          <path d="M12 7c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" fill="#ef4444"/>
        </svg>`
      },
      checkpoint: {
        color: '#f59e0b',
        svg: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
          <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM9 6c0-1.66 1.34-3 3-3s3 1.34 3 3v2H9V6zm3 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/>
        </svg>`
      }
    };

    const config = iconConfigs[type as keyof typeof iconConfigs] || iconConfigs.checkpoint;

    return {
      url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
        <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
          <circle cx="20" cy="20" r="18" fill="${config.color}" stroke="white" stroke-width="2"/>
          <g transform="translate(8, 8) scale(0.5)">
            ${config.svg}
          </g>
        </svg>
      `),
      scaledSize: new google.maps.Size(40, 40),
      anchor: new google.maps.Point(20, 20)
    };
  };

  // Load Google Maps API
  useEffect(() => {
    console.log('🗺️ MapDemo2: Starting...');

    if (typeof window === 'undefined') {
      console.log('⏳ MapDemo2: Window not available yet');
      return;
    }

    // Check if already loaded
    if (window.google && window.google.maps) {
      console.log('✅ MapDemo2: Google Maps already loaded');
      setIsLoaded(true);
      return;
    }

    console.log('📡 MapDemo2: Loading Google Maps API...');
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`;
    script.async = true;
    script.defer = true;

    script.onload = () => {
      console.log('✅ MapDemo2: Google Maps script loaded');
      setIsLoaded(true);
    };

    script.onerror = () => {
      console.error('❌ MapDemo2: Failed to load Google Maps');
    };

    document.head.appendChild(script);
  }, []);

  // Initialize map
  useEffect(() => {
    if (!isLoaded || !mapRef.current) {
      console.log('⏳ MapDemo2: Map init skipped - isLoaded:', isLoaded, 'hasRef:', !!mapRef.current);
      return;
    }

    console.log('🗺️ MapDemo2: Creating map...');

    const mapInstance = new google.maps.Map(mapRef.current, {
      center: { lat: 24.7136, lng: 46.6753 }, // Riyadh
      zoom: 6,
    });

    setMap(mapInstance);
    console.log('✅ MapDemo2: Map created successfully');
  }, [isLoaded]);

  // Add markers
  useEffect(() => {
    if (!map) {
      console.log('⏳ MapDemo2: No map for markers yet');
      return;
    }

    console.log('📍 MapDemo2: Adding markers...');

    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));

    const newMarkers = sampleLocations.map((location, index) => {
      console.log(`📍 Creating marker ${index + 1}:`, location.name, location.type);

      const marker = new google.maps.Marker({
        position: { lat: location.lat, lng: location.lng },
        map: map,
        title: location.name,
        icon: createSVGMarker(location.type)
      });

      // Add info window
      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div style="padding: 8px; min-width: 200px;">
            <h3 style="font-weight: bold; font-size: 14px; margin-bottom: 4px; color: #1f2937;">${location.name}</h3>
            <p style="font-size: 12px; color: #6b7280; margin-bottom: 2px;">📍 Location Type: ${location.type.replace('_', ' ')}</p>
            <p style="font-size: 12px; color: #2563eb; text-transform: capitalize; font-weight: 500;">${location.type.replace('_', ' ')}</p>
          </div>
        `
      });

      marker.addListener('click', () => {
        infoWindow.open(map, marker);
      });

      return marker;
    });

    setMarkers(newMarkers);
    console.log('✅ MapDemo2: Added', newMarkers.length, 'markers');
  }, [map]);

  if (!isLoaded) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-gray-100">
        <div className="bg-white rounded-lg border p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Map</h3>
          <p className="text-gray-500">Loading Google Maps...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 w-full h-full">
      {/* Full-size map */}
      <div
        ref={mapRef}
        className="absolute inset-0 w-full h-full"
      />

      {/* Expandable button */}
      <button
        onClick={() => setSidebarOpen(!sidebarOpen)}
        className="fixed top-1/2 right-2 transform -translate-y-1/2 bg-blue-600 text-white rounded-full shadow-xl border-2 border-white p-4 hover:bg-blue-700 transition-all duration-200"
        style={{ zIndex: 1001 }}
      >
        {sidebarOpen ? (
          <ChevronRight className="w-6 h-6" />
        ) : (
          <ChevronLeft className="w-6 h-6" />
        )}
      </button>

      {/* Simple sidebar - positioned from page content area */}
      {sidebarOpen && (
        <div
          className="fixed right-0 bg-white shadow-xl border-l w-96 overflow-y-auto"
          style={{
            top: '112px',   // Start below header (48px + 64px = 112px)
            bottom: '40px', // End above footer (increased to 80px for better clearance)
            zIndex: 999
          }}
        >
          <div className="h-full flex flex-col relative">
            {/* Tab Navigation */}
            <div className="flex border-b bg-gray-50">
              {['search', 'alerts', 'routes'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`flex-1 px-4 py-3 text-sm font-medium capitalize ${
                    activeTab === tab
                      ? 'bg-green-500 text-white'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                  }`}
                >
                  {tab}
                </button>
              ))}
            </div>

            {/* Measure Distance Button */}
            <div className="p-4 border-b">
              <button className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                Measure Distance
              </button>
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-y-auto">
              {activeTab === 'search' && (
                <div className="p-4 space-y-4">
                  {/* Filter Checkboxes */}
                  <div className="space-y-3">
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedFilters.showPort}
                        onChange={(e) => setSelectedFilters(prev => ({...prev, showPort: e.target.checked}))}
                        className="rounded text-blue-600"
                      />
                      <span className="text-sm">Show Port</span>
                    </label>
                    <label className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedFilters.showCheckpost}
                        onChange={(e) => setSelectedFilters(prev => ({...prev, showCheckpost: e.target.checked}))}
                        className="rounded text-blue-600"
                      />
                      <span className="text-sm">Show CheckPost/Police Station</span>
                    </label>
                  </div>

                  {/* Trip Code Dropdown */}
                  <div>
                    <select
                      value={selectedFilters.tripCode}
                      onChange={(e) => setSelectedFilters(prev => ({...prev, tripCode: e.target.value}))}
                      className="w-full p-2 border border-gray-300 rounded text-sm"
                    >
                      <option value="Trip Code">Trip Code</option>
                      <option value="Driver Name">Driver Name</option>
                      <option value="Vehicle Plate Number">Vehicle Plate Number</option>
                      <option value="E-locks">E-locks</option>
                      <option value="Device Id">Device Id</option>
                      <option value="Entry Port Name">Entry Port Name</option>
                      <option value="Exit Port Name">Exit Port Name</option>
                      <option value="Transit Number">Transit Number</option>
                      <option value="Transit Sequence">Transit Sequence</option>
                      <option value="Transit Date">Transit Date</option>
                    </select>
                  </div>

                  {/* Search Input */}
                  <div>
                    <input
                      type="text"
                      value={selectedFilters.searchValue}
                      onChange={(e) => setSelectedFilters(prev => ({...prev, searchValue: e.target.value}))}
                      placeholder="Enter search value..."
                      className="w-full p-2 border border-gray-300 rounded text-sm"
                    />
                  </div>

                  {/* Reset Button */}
                  <div className="pt-2">
                    <button
                      onClick={() => setSelectedFilters({
                        showPort: true,
                        showCheckpost: true,
                        tripCode: 'Trip Code',
                        searchValue: ''
                      })}
                      className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors text-sm"
                    >
                      Reset
                    </button>
                  </div>

                  {/* Charts Section */}
                  <div className="border-t pt-4 mt-6">
                    <h3 className="font-semibold mb-4 text-gray-800">Trip Statistics</h3>

                    {/* Pie Chart */}
                    <div className="mb-6">
                      <h4 className="text-sm font-medium mb-2">Active Trips</h4>
                      <div className="h-64 flex flex-col items-center">
                        <div className="relative w-48 h-48">
                          <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                              <Pie
                                data={pieData}
                                cx="50%"
                                cy="50%"
                                outerRadius={80}
                                dataKey="value"
                                startAngle={90}
                                endAngle={450}
                              >
                                {pieData.map((entry, index) => (
                                  <Cell key={`cell-${index}`} fill={entry.color} />
                                ))}
                              </Pie>
                              <Tooltip formatter={(value, name) => [value, name]} />
                            </PieChart>
                          </ResponsiveContainer>
                          {/* Center text showing total without alerts */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="text-2xl font-bold text-white bg-orange-500 rounded-full w-16 h-16 flex items-center justify-center">
                              {pieData[1].value}
                            </span>
                          </div>
                        </div>

                        {/* Custom Legend */}
                        <div className="mt-4 space-y-2 text-sm">
                          <div className="flex items-center justify-center space-x-2">
                            <div className="w-3 h-3 bg-blue-500"></div>
                            <span>Active Trips With Alerts ({pieData[0].value})</span>
                          </div>
                          <div className="flex items-center justify-center space-x-2">
                            <div className="w-3 h-3 bg-orange-500"></div>
                            <span>Active Trips Without Alerts ({pieData[1].value})</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Bar Chart */}
                    <div>
                      <h4 className="text-sm font-medium mb-2">Route Analytics</h4>
                      <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={barData}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis
                              dataKey="name"
                              axisLine={true}
                              tickLine={true}
                            />
                            <YAxis
                              domain={[0, 1600]}
                              ticks={[0, 200, 400, 600, 800, 1000, 1200, 1400, 1600]}
                              axisLine={true}
                              tickLine={true}
                            />
                            <Tooltip
                              formatter={(value, name) => [value, name]}
                              labelFormatter={(label) => `Route: ${label}`}
                            />
                            <Bar dataKey="Active Trips" fill="#dc2626" name="Active Trips" />
                            <Bar dataKey="Active Trips With Alerts" fill="#3b82f6" name="Active Trips With Alerts" />
                            <Bar dataKey="Active Trips With Communication Lost" fill="#22c55e" name="Active Trips With Communication Lost" />
                          </BarChart>
                        </ResponsiveContainer>

                        {/* Custom Legend */}
                        <div className="mt-4 space-y-1 text-xs">
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-red-600"></div>
                            <span>Active Trips</span>
                            <span className="ml-auto font-medium">1562</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-blue-500"></div>
                            <span>Active Trips With Alerts</span>
                            <span className="ml-auto font-medium">11</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-green-500"></div>
                            <span>Active Trips With Communication Lost</span>
                            <span className="ml-auto font-medium">1</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'alerts' && (
                <div className="p-4">
                  <h3 className="font-semibold mb-4 text-gray-800">Recent Alerts</h3>
                  <div className="space-y-3">
                    {alertsData.map((alert) => (
                      <div key={alert.id} className="border rounded-lg p-3 bg-white shadow-sm">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-sm">{alert.title}</h4>
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                alert.severity === 'high' ? 'bg-red-100 text-red-800' :
                                alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-blue-100 text-blue-800'
                              }`}>
                                {alert.severity}
                              </span>
                            </div>
                            <p className="text-xs text-gray-600 mt-1">{alert.description}</p>
                            <p className="text-xs text-gray-500 mt-2">{alert.time}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'routes' && (
                <div className="p-4 space-y-4">
                  {/* Route Filter Buttons */}
                  <div className="flex w-full gap-1">
                    <button
                      onClick={() => {
                        setRouteFilter('my-routes');
                        // Pre-populate with user's saved routes
                        setSelectedRoutes([...myRoutes]);
                      }}
                      className={`flex-1 py-2 px-3 text-sm font-medium rounded-sm transition-colors ${
                        routeFilter === 'my-routes'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
                      }`}
                    >
                      My Routes
                    </button>
                    <button
                      onClick={() => {
                        setRouteFilter('all');
                        // Pre-populate with all available routes
                        setSelectedRoutes([...routeOptions]);
                      }}
                      className={`flex-1 py-2 px-3 text-sm font-medium rounded-sm transition-colors ${
                        routeFilter === 'all'
                          ? 'bg-green-500 text-white'
                          : 'bg-gray-300 text-gray-700 hover:bg-gray-400'
                      }`}
                    >
                      All
                    </button>
                    <button
                      onClick={() => {
                        setRouteFilter('my-routes');
                        setRouteName('');
                        setSelectedRoutes([]);
                      }}
                      className="flex-1 py-2 px-3 text-sm font-medium bg-blue-500 text-white rounded-sm hover:bg-blue-600 transition-colors"
                    >
                      Reset
                    </button>
                  </div>

                  {/* Multi-Select Route Input */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 text-center">Route Name</label>

                    {/* Selected Routes Tags */}
                    <div className="min-h-[120px] border border-gray-300 rounded p-3 bg-white">
                      <div className="flex flex-wrap gap-2">
                        {selectedRoutes.map((route, index) => (
                          <div
                            key={index}
                            className="flex items-center bg-gray-100 text-gray-800 px-3 py-1 rounded text-sm border"
                          >
                            <span className="mr-2">{route}</span>
                            <button
                              onClick={() => {
                                setSelectedRoutes(prev => prev.filter((_, i) => i !== index));
                              }}
                              className="text-gray-500 hover:text-gray-700 font-bold text-lg leading-none"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Search Input */}
                    <div className="relative">
                      <input
                        type="text"
                        value={routeName}
                        onChange={(e) => {
                          const value = e.target.value;
                          setRouteName(value);

                          // Filter routes based on input and exclude already selected
                          if (value.length > 0) {
                            const filtered = routeOptions.filter(route =>
                              route.toLowerCase().includes(value.toLowerCase()) &&
                              !selectedRoutes.includes(route)
                            );
                            setFilteredRoutes(filtered);
                            setShowSuggestions(true);
                          } else {
                            setFilteredRoutes([]);
                            setShowSuggestions(false);
                          }
                        }}
                        onFocus={() => {
                          if (routeName.length > 0) {
                            const filtered = routeOptions.filter(route =>
                              route.toLowerCase().includes(routeName.toLowerCase()) &&
                              !selectedRoutes.includes(route)
                            );
                            setFilteredRoutes(filtered);
                            setShowSuggestions(true);
                          }
                        }}
                        onBlur={() => {
                          // Delay hiding suggestions to allow for clicks
                          setTimeout(() => setShowSuggestions(false), 200);
                        }}
                        placeholder="Type to search and add routes..."
                        className="w-full p-2 border border-gray-300 rounded text-sm"
                      />

                      {/* Autocomplete Suggestions */}
                      {showSuggestions && filteredRoutes.length > 0 && (
                        <div className="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto mt-1">
                          {filteredRoutes.map((route, index) => (
                            <div
                              key={index}
                              onClick={() => {
                                if (!selectedRoutes.includes(route)) {
                                  setSelectedRoutes(prev => [...prev, route]);
                                }
                                setRouteName('');
                                setShowSuggestions(false);
                                setFilteredRoutes([]);
                              }}
                              className="px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                            >
                              {route}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Fixed close button at bottom */}
            <div className="pt-4 border-t">
              <button
                onClick={() => setSidebarOpen(false)}
                className="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
              >
                Close Sidebar
              </button>
            </div>

            {/* Close Sidebar Button */}
            <div className="absolute bottom-0 left-0 right-0 bg-red-500 text-white">
              <button
                onClick={() => setSidebarOpen(false)}
                className="w-full py-3 text-center font-medium hover:bg-red-600 transition-colors"
              >
                Close Sidebar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}