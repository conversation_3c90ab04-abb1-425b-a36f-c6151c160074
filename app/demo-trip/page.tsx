'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Eye, Truck, Package, MapPin, Clock } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { PageTemplate } from '../../components/layout';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  getDemoTrips, 
  DemoTrip, 
  getTripStatusIcon, 
  getTripStatusColor, 
  getTripTypeIcon,
  formatTripDate,
  calculateTripProgress
} from '../../api/demo_trips';

export default function DemoTripPage() {
  const { t } = useLanguage();
  const [trips, setTrips] = useState<DemoTrip[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTrips = async () => {
      try {
        setLoading(true);
        const response = await getDemoTrips(1, 20); // Get first 20 trips
        setTrips(response.trips);
      } catch (err) {
        setError('Failed to load trips');
        console.error('Error fetching trips:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchTrips();
  }, []);

  if (loading) {
    return (
      <PageTemplate titleKey="demoTrip.title">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading trips...</span>
        </div>
      </PageTemplate>
    );
  }

  if (error) {
    return (
      <PageTemplate titleKey="demoTrip.title">
        <div className="text-center py-12">
          <div className="text-red-600 mb-4">{error}</div>
          <Button onClick={() => window.location.reload()}>
            Retry
          </Button>
        </div>
      </PageTemplate>
    );
  }

  return (
    <PageTemplate 
      titleKey="demoTrip.title" 
      description={t('demoTrip.description')}
    >
      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <Truck className="w-8 h-8 text-blue-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-blue-900">
                  {trips.filter(t => t.tripStatus === 'activated').length}
                </div>
                <div className="text-sm text-blue-700">Active Trips</div>
              </div>
            </div>
          </div>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <Package className="w-8 h-8 text-green-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-green-900">
                  {trips.filter(t => t.tripStatus === 'ended').length}
                </div>
                <div className="text-sm text-green-700">Completed</div>
              </div>
            </div>
          </div>
          
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-yellow-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-yellow-900">
                  {trips.filter(t => t.tripStatus === 'pending').length}
                </div>
                <div className="text-sm text-yellow-700">Pending</div>
              </div>
            </div>
          </div>
          
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <MapPin className="w-8 h-8 text-red-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-red-900">
                  {trips.filter(t => t.tripStatus === 'cancelled').length}
                </div>
                <div className="text-sm text-red-700">Cancelled</div>
              </div>
            </div>
          </div>
        </div>

        {/* Desktop Table View */}
        <div className="hidden md:block bg-white rounded-lg shadow overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('demoTrip.moreInfo')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('demoTrip.tripNumber')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('demoTrip.shipment')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('demoTrip.route')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('demoTrip.driver')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('demoTrip.status')}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t('demoTrip.progress')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {trips.map((trip) => (
                  <tr key={trip.tripId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Link href={`/demo-trip/${trip.tripId}`}>
                        <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-800">
                          <Eye className="w-4 h-4" />
                        </Button>
                      </Link>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{trip.transitNumber}</div>
                      <div className="text-sm text-gray-500">{getTripTypeIcon(trip.transitType)} {trip.transitType}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{trip.shipment.shipmentDescription}</div>
                      <div className="text-sm text-gray-500">{trip.shipment.shipmentId}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{trip.route.entryPort}</div>
                      <div className="text-sm text-gray-500">→ {trip.route.exitPort}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{trip.driver.driverName}</div>
                      <div className="text-sm text-gray-500">{trip.vehicle.vehiclePlateNumber}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTripStatusColor(trip.tripStatus)}`}>
                        {getTripStatusIcon(trip.tripStatus)} {trip.tripStatus}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${calculateTripProgress(trip)}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600">{calculateTripProgress(trip)}%</span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile Card View */}
        <div className="md:hidden space-y-4">
          {trips.map((trip) => (
            <div key={trip.tripId} className="bg-white rounded-lg shadow p-4 border">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <div className="font-medium text-gray-900">{trip.transitNumber}</div>
                  <div className="text-sm text-gray-500">{getTripTypeIcon(trip.transitType)} {trip.transitType}</div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTripStatusColor(trip.tripStatus)}`}>
                    {getTripStatusIcon(trip.tripStatus)} {trip.tripStatus}
                  </span>
                  <Link href={`/demo-trip/${trip.tripId}`}>
                    <Button variant="ghost" size="sm" className="text-blue-600">
                      <Eye className="w-4 h-4" />
                    </Button>
                  </Link>
                </div>
              </div>
              
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Shipment:</span> {trip.shipment.shipmentDescription}
                </div>
                <div>
                  <span className="font-medium">Driver:</span> {trip.driver.driverName}
                </div>
                <div>
                  <span className="font-medium">Route:</span> {trip.route.entryPort} → {trip.route.exitPort}
                </div>
                <div className="flex items-center">
                  <span className="font-medium mr-2">Progress:</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${calculateTripProgress(trip)}%` }}
                    ></div>
                  </div>
                  <span className="text-xs text-gray-600">{calculateTripProgress(trip)}%</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {trips.length === 0 && (
          <div className="text-center py-12">
            <Truck className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No trips found</h3>
            <p className="text-gray-500">There are no trips to display at the moment.</p>
          </div>
        )}
      </div>
    </PageTemplate>
  );
}
