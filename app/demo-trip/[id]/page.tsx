'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  ArrowLeft, 
  Truck, 
  User, 
  MapPin, 
  Package, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Calendar,
  Navigation
} from 'lucide-react';
import { Button } from '../../../components/ui/button';
import { PageTemplate } from '../../../components/layout';
import { useLanguage } from '../../../contexts/LanguageContext';
import {
  getDemoTripById,
  DemoTrip,
  getTripStatusIcon,
  getTripStatusColor,
  getTripTypeIcon,
  formatTripDate,
  calculateTripProgress,
  formatDistance
} from '../../../api/demo_trips';

export default function DemoTripDetailPage() {
  const { t } = useLanguage();
  const params = useParams();
  const router = useRouter();
  const [trip, setTrip] = useState<DemoTrip | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTrip = async () => {
      try {
        setLoading(true);
        const tripId = params.id as string;
        const tripData = await getDemoTripById(tripId);
        
        if (!tripData) {
          setError('Trip not found');
          return;
        }
        
        setTrip(tripData);
      } catch (err) {
        setError('Failed to load trip details');
        console.error('Error fetching trip:', err);
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchTrip();
    }
  }, [params.id]);

  if (loading) {
    return (
      <PageTemplate titleKey="demoTrip.tripDetails">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading trip details...</span>
        </div>
      </PageTemplate>
    );
  }

  if (error || !trip) {
    return (
      <PageTemplate titleKey="demoTrip.tripDetails">
        <div className="text-center py-12">
          <div className="text-red-600 mb-4">{error || 'Trip not found'}</div>
          <div className="space-x-4">
            <Button onClick={() => router.back()}>
              Go Back
            </Button>
            <Link href="/demo-trip">
              <Button variant="outline">
                {t('demoTrip.backToTable')}
              </Button>
            </Link>
          </div>
        </div>
      </PageTemplate>
    );
  }

  return (
    <PageTemplate titleKey="demoTrip.tripDetails">
      <div className="space-y-6">
        {/* Header with Back Button */}
        <div className="flex items-center justify-between">
          <Link href="/demo-trip">
            <Button variant="outline" className="flex items-center">
              <ArrowLeft className="w-4 h-4 mr-2" />
              {t('demoTrip.backToTable')}
            </Button>
          </Link>
          
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getTripStatusColor(trip.tripStatus)}`}>
            {getTripStatusIcon(trip.tripStatus)} {trip.tripStatus}
          </span>
        </div>

        {/* Trip Overview Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl mb-2">{getTripTypeIcon(trip.transitType)}</div>
              <div className="text-lg font-semibold text-gray-900">{trip.transitNumber}</div>
              <div className="text-sm text-gray-500">{trip.transitType}</div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-2">📊</div>
              <div className="text-lg font-semibold text-gray-900">{calculateTripProgress(trip)}%</div>
              <div className="text-sm text-gray-500">Progress</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${calculateTripProgress(trip)}%` }}
                ></div>
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-2">🛣️</div>
              <div className="text-lg font-semibold text-gray-900">{formatDistance(trip.tracking.completeDistance)}</div>
              <div className="text-sm text-gray-500">Completed</div>
            </div>
            
            <div className="text-center">
              <div className="text-3xl mb-2">🎯</div>
              <div className="text-lg font-semibold text-gray-900">{formatDistance(trip.tracking.remainingDistance)}</div>
              <div className="text-sm text-gray-500">Remaining</div>
            </div>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Driver Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <User className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">{t('demoTrip.driver')}</h3>
            </div>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Name:</span>
                <div className="text-gray-900">{trip.driver.driverName}</div>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Passport:</span>
                <div className="text-gray-900">{trip.driver.driverPassportNumber}</div>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Nationality:</span>
                <div className="text-gray-900">{trip.driver.driverNationality}</div>
              </div>
              {trip.driver.driverContactNo && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Contact Number:</span>
                  <div className="text-gray-900">{trip.driver.driverContactNo}</div>
                </div>
              )}
            </div>
          </div>

          {/* Vehicle Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <Truck className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">{t('demoTrip.vehicle')}</h3>
            </div>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Plate Number:</span>
                <div className="text-gray-900">{trip.vehicle.vehiclePlateNumber}</div>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Vehicle ID:</span>
                <div className="text-gray-900">{trip.vehicle.vehicleId}</div>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Tracker No:</span>
                <div className="text-gray-900">{trip.vehicle.trackerNo}</div>
              </div>
              {trip.vehicle.model && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Model:</span>
                  <div className="text-gray-900">{trip.vehicle.model}</div>
                </div>
              )}
              {trip.vehicle.color && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Color:</span>
                  <div className="text-gray-900">{trip.vehicle.color}</div>
                </div>
              )}
              {trip.vehicle.type && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Type:</span>
                  <div className="text-gray-900">{trip.vehicle.type}</div>
                </div>
              )}
              {trip.vehicle.plateCountry && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Plate Country:</span>
                  <div className="text-gray-900">{trip.vehicle.plateCountry}</div>
                </div>
              )}
            </div>
          </div>

          {/* Route Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <MapPin className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">{t('demoTrip.route')}</h3>
            </div>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Route Name:</span>
                <div className="text-gray-900">{trip.route.routeName}</div>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Entry Port:</span>
                <div className="text-gray-900">{trip.route.entryPort}</div>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Exit Port:</span>
                <div className="text-gray-900">{trip.route.exitPort}</div>
              </div>
            </div>
          </div>

          {/* Shipment Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <Package className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">{t('demoTrip.shipment')}</h3>
            </div>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Shipment ID:</span>
                <div className="text-gray-900">{trip.shipment.shipmentId}</div>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Description:</span>
                <div className="text-gray-900">{trip.shipment.shipmentDescription}</div>
              </div>
              {trip.shipment.ownerDescription && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Owner:</span>
                  <div className="text-gray-900">{trip.shipment.ownerDescription}</div>
                </div>
              )}
              {trip.shipment.shipmentDescriptionArabic && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Description (Arabic):</span>
                  <div className="text-gray-900">{trip.shipment.shipmentDescriptionArabic}</div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Additional Trip Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Trip Details */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <AlertCircle className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Additional Details</h3>
            </div>
            <div className="space-y-3">
              {trip.transitSeqNo && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Transit Sequence No:</span>
                  <div className="text-gray-900">{trip.transitSeqNo}</div>
                </div>
              )}
              {trip.declarationDate && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Declaration Date:</span>
                  <div className="text-gray-900">{trip.declarationDate}</div>
                </div>
              )}
              {trip.tracking.elocks && (
                <div>
                  <span className="text-sm font-medium text-gray-500">E-Locks:</span>
                  <div className="text-gray-900">{trip.tracking.elocks}</div>
                </div>
              )}
            </div>
          </div>

          {/* Date Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <Clock className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Date Information</h3>
            </div>
            <div className="space-y-3">
              {trip.startingDate && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Starting Date:</span>
                  <div className="text-gray-900">{trip.startingDate}</div>
                </div>
              )}
              {trip.expectedArrivalDate && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Expected Arrival:</span>
                  <div className="text-gray-900">{trip.expectedArrivalDate}</div>
                </div>
              )}
              {trip.endDate && (
                <div>
                  <span className="text-sm font-medium text-gray-500">End Date:</span>
                  <div className="text-gray-900">{trip.endDate}</div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Timeline and Tracking */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Trip Timeline */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <Calendar className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Trip Timeline</h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></div>
                <div>
                  <div className="text-sm font-medium text-gray-900">Created</div>
                  <div className="text-sm text-gray-500">{formatTripDate(trip.creationDate)}</div>
                </div>
              </div>
              
              {trip.activationDate && (
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-2 h-2 bg-green-600 rounded-full mt-2 mr-3"></div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">Activated</div>
                    <div className="text-sm text-gray-500">{formatTripDate(trip.activationDate)}</div>
                  </div>
                </div>
              )}
              
              {trip.completionDate && (
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-2 h-2 bg-gray-600 rounded-full mt-2 mr-3"></div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">Completed</div>
                    <div className="text-sm text-gray-500">{formatTripDate(trip.completionDate)}</div>
                  </div>
                </div>
              )}
              
              {trip.tracking.estimatedArrival && (
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-2 h-2 bg-yellow-600 rounded-full mt-2 mr-3"></div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">Estimated Arrival</div>
                    <div className="text-sm text-gray-500">{formatTripDate(trip.tracking.estimatedArrival)}</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Compliance Status */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center mb-4">
              <CheckCircle className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">{t('demoTrip.compliance')}</h3>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">Customs Status:</span>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  trip.compliance.customsStatus === 'cleared' ? 'text-green-700 bg-green-100' :
                  trip.compliance.customsStatus === 'pending' ? 'text-yellow-700 bg-yellow-100' :
                  'text-red-700 bg-red-100'
                }`}>
                  {trip.compliance.customsStatus}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-500">Document Status:</span>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                  trip.compliance.documentStatus === 'complete' ? 'text-green-700 bg-green-100' :
                  trip.compliance.documentStatus === 'pending' ? 'text-yellow-700 bg-yellow-100' :
                  'text-red-700 bg-red-100'
                }`}>
                  {trip.compliance.documentStatus}
                </span>
              </div>
              
              <div>
                <span className="text-sm font-medium text-gray-500">Security Notes:</span>
                <div className="text-sm text-gray-900 mt-1">{trip.compliance.securityNotes}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Current Location */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center mb-4">
            <Navigation className="w-5 h-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900">Current Location</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <span className="text-sm font-medium text-gray-500">Latitude:</span>
              <div className="text-gray-900">{trip.tracking.currentLocation.latitude}</div>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Longitude:</span>
              <div className="text-gray-900">{trip.tracking.currentLocation.longitude}</div>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500">Last Update:</span>
              <div className="text-gray-900">{formatTripDate(trip.tracking.currentLocation.timestamp)}</div>
            </div>
          </div>
        </div>
      </div>
    </PageTemplate>
  );
}
