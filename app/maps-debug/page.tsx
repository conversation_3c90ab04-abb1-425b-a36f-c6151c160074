'use client';

import React, { useEffect, useState } from 'react';

/**
 * Maps Debug Page
 * Helps diagnose Google Maps API issues
 */
export default function MapsDebugPage() {
  const [debugInfo, setDebugInfo] = useState({
    apiKey: '',
    apiKeyLength: 0,
    environment: '',
    userAgent: '',
    isLocalhost: false,
    googleMapsLoaded: false,
    consoleErrors: [] as string[],
  });

  useEffect(() => {
    // Capture console errors
    const originalError = console.error;
    const errors: string[] = [];
    
    console.error = (...args) => {
      errors.push(args.join(' '));
      originalError(...args);
    };

    // Collect debug information
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';
    
    setDebugInfo({
      apiKey: apiKey ? `${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 5)}` : 'Not set',
      apiKeyLength: apiKey.length,
      environment: process.env.NODE_ENV || 'unknown',
      userAgent: navigator.userAgent,
      isLocalhost: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1',
      googleMapsLoaded: !!(window as any).google?.maps,
      consoleErrors: errors,
    });

    // Test API key by making a simple request
    if (apiKey) {
      testApiKey(apiKey);
    }

    return () => {
      console.error = originalError;
    };
  }, []);

  const testApiKey = async (apiKey: string) => {
    try {
      // Test the API key with a simple geocoding request
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=Riyadh&key=${apiKey}`
      );
      const data = await response.json();
      
      if (data.error_message) {
        console.error('API Key Error:', data.error_message);
      } else if (data.status === 'OK') {
        console.log('✅ API Key is working correctly');
      } else {
        console.warn('API Key test returned status:', data.status);
      }
    } catch (error) {
      console.error('Failed to test API key:', error);
    }
  };

  const checkApiKeyRestrictions = () => {
    const currentDomain = window.location.hostname;
    const currentPort = window.location.port;
    const fullDomain = currentPort ? `${currentDomain}:${currentPort}` : currentDomain;
    
    return {
      currentDomain: fullDomain,
      commonRestrictions: [
        'localhost:3000/*',
        '127.0.0.1:3000/*',
        `${fullDomain}/*`,
        '*.vercel.app/*',
        '*.netlify.app/*'
      ]
    };
  };

  const restrictions = checkApiKeyRestrictions();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Google Maps Debug Information
          </h1>
          <p className="text-gray-600">
            This page helps diagnose issues with Google Maps integration.
          </p>
        </div>

        {/* API Key Status */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">API Key Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">API Key:</span>
                <span className={`font-mono text-sm ${debugInfo.apiKeyLength > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {debugInfo.apiKey}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Key Length:</span>
                <span className={debugInfo.apiKeyLength === 39 ? 'text-green-600' : 'text-yellow-600'}>
                  {debugInfo.apiKeyLength} chars {debugInfo.apiKeyLength === 39 ? '✅' : '⚠️'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Environment:</span>
                <span className="text-blue-600">{debugInfo.environment}</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Current Domain:</span>
                <span className="font-mono text-sm">{restrictions.currentDomain}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Is Localhost:</span>
                <span className={debugInfo.isLocalhost ? 'text-green-600' : 'text-blue-600'}>
                  {debugInfo.isLocalhost ? 'Yes ✅' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Google Maps Loaded:</span>
                <span className={debugInfo.googleMapsLoaded ? 'text-green-600' : 'text-red-600'}>
                  {debugInfo.googleMapsLoaded ? 'Yes ✅' : 'No ❌'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Common Issues */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Common Issues & Solutions</h2>
          <div className="space-y-4">
            
            {/* API Key Missing */}
            {debugInfo.apiKeyLength === 0 && (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                <h3 className="font-semibold text-red-900 mb-2">❌ API Key Missing</h3>
                <p className="text-red-800 text-sm mb-2">Your Google Maps API key is not configured.</p>
                <div className="text-red-700 text-sm">
                  <strong>Solution:</strong> Add your API key to <code>.env.local</code>:
                  <div className="bg-red-100 p-2 mt-1 rounded font-mono text-xs">
                    NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_actual_api_key
                  </div>
                </div>
              </div>
            )}

            {/* API Key Length Issue */}
            {debugInfo.apiKeyLength > 0 && debugInfo.apiKeyLength !== 39 && (
              <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                <h3 className="font-semibold text-yellow-900 mb-2">⚠️ API Key Length Issue</h3>
                <p className="text-yellow-800 text-sm mb-2">
                  Google Maps API keys are typically 39 characters long. Your key is {debugInfo.apiKeyLength} characters.
                </p>
                <p className="text-yellow-700 text-sm">
                  <strong>Solution:</strong> Double-check that you copied the complete API key from Google Cloud Console.
                </p>
              </div>
            )}

            {/* Referrer Restrictions */}
            <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">🔒 API Key Restrictions</h3>
              <p className="text-blue-800 text-sm mb-2">
                Make sure your API key allows requests from: <code className="bg-blue-100 px-1 rounded">{restrictions.currentDomain}</code>
              </p>
              <div className="text-blue-700 text-sm">
                <strong>Common restriction patterns:</strong>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  {restrictions.commonRestrictions.map((restriction, index) => (
                    <li key={index} className="font-mono text-xs">{restriction}</li>
                  ))}
                </ul>
              </div>
            </div>

            {/* APIs Not Enabled */}
            <div className="bg-purple-50 border border-purple-200 p-4 rounded-lg">
              <h3 className="font-semibold text-purple-900 mb-2">🔧 Required APIs</h3>
              <p className="text-purple-800 text-sm mb-2">
                Make sure these APIs are enabled in Google Cloud Console:
              </p>
              <ul className="text-purple-700 text-sm list-disc list-inside space-y-1">
                <li><strong>Maps JavaScript API</strong> (Required)</li>
                <li>Places API (Optional)</li>
                <li>Geocoding API (Optional)</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Browser Console Errors */}
        {debugInfo.consoleErrors.length > 0 && (
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Console Errors</h2>
            <div className="bg-gray-900 text-red-400 p-4 rounded-lg font-mono text-sm max-h-60 overflow-y-auto">
              {debugInfo.consoleErrors.map((error, index) => (
                <div key={index} className="mb-1">{error}</div>
              ))}
            </div>
          </div>
        )}

        {/* Test Links */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Test Pages</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/maps-simple"
              className="block p-4 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <h3 className="font-semibold text-blue-900 mb-1">Simple Map</h3>
              <p className="text-blue-700 text-sm">Basic map with markers</p>
            </a>
            <a
              href="/maps-test"
              className="block p-4 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
            >
              <h3 className="font-semibold text-green-900 mb-1">Advanced Features</h3>
              <p className="text-green-700 text-sm">Vehicle tracking, routes</p>
            </a>
            <a
              href="/maps-setup"
              className="block p-4 bg-purple-50 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <h3 className="font-semibold text-purple-900 mb-1">Setup Guide</h3>
              <p className="text-purple-700 text-sm">Step-by-step configuration</p>
            </a>
          </div>
        </div>

        {/* Manual API Test */}
        <div className="mt-6 bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Manual API Test</h3>
          <p className="text-gray-600 text-sm mb-2">
            Open browser console (F12) and run this command to test your API key:
          </p>
          <div className="bg-gray-900 text-green-400 p-3 rounded font-mono text-sm">
            {`fetch('https://maps.googleapis.com/maps/api/geocode/json?address=Riyadh&key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}').then(r=>r.json()).then(console.log)`}
          </div>
        </div>
      </div>
    </div>
  );
}
