'use client';

import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Shield, Phone, Clock, Users, AlertTriangle } from 'lucide-react';
import { loadGoogleMapsAPI } from '../../utils/googleMaps';

// Simple marker data for demonstration
const sampleMarkers = [
  {
    id: '1',
    name: 'King Fahd District Police Station',
    lat: 24.7136,
    lng: 46.6753,
    address: 'King Fahd Road, Riyadh',
    phone: '+966-11-401-1234',
    officers: 45
  },
  {
    id: '2',
    name: 'Olaya District Police Station',
    lat: 24.6877,
    lng: 46.6947,
    address: 'Olaya Street, Riyadh',
    phone: '+966-11-401-2345',
    officers: 38
  },
  {
    id: '3',
    name: 'Al-Malaz Police Station',
    lat: 24.6408,
    lng: 46.7146,
    address: 'Al-Malaz District, Riyadh',
    phone: '+966-11-401-3456',
    officers: 42
  },
  {
    id: '4',
    name: 'Diplomatic Quarter Police Station',
    lat: 24.6892,
    lng: 46.6197,
    address: 'Diplomatic Quarter, Riyadh',
    phone: '+966-11-401-4567',
    officers: 52
  },
  {
    id: '5',
    name: 'Al-Naseem Police Station',
    lat: 24.6234,
    lng: 46.6891,
    address: 'Al-Naseem District, Riyadh',
    phone: '+966-11-401-5678',
    officers: 35
  }
];

interface SimpleMarker {
  id: string;
  name: string;
  lat: number;
  lng: number;
  address: string;
  phone: string;
  officers: number;
}



export default function MapDemo() {
  // Step 1: Set up component state
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [selectedMarker, setSelectedMarker] = useState<SimpleMarker | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Step 2: Load Google Maps API
  useEffect(() => {
    loadGoogleMapsAPI()
      .then(() => {
        setIsLoaded(true);
        setError(null);
      })
      .catch((err) => {
        setError(err.message);
        console.error('Error loading Google Maps API:', err);
      });
  }, []);

  // Step 3: Initialize the map
  useEffect(() => {
    if (!isLoaded || !mapRef.current) return;

    const mapInstance = new google.maps.Map(mapRef.current, {
      center: { lat: 24.7136, lng: 46.6753 }, // Riyadh center
      zoom: 11,
      // Optional: Hide default points of interest for cleaner look
      styles: [
        {
          featureType: 'poi',
          elementType: 'labels',
          stylers: [{ visibility: 'off' }]
        }
      ]
    });

    setMap(mapInstance);
  }, [isLoaded]);

  // Step 4: Add markers to the map
  useEffect(() => {
    if (!map) return;

    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));

    const newMarkers = sampleMarkers.map(markerData => {
      // Create a marker
      const marker = new google.maps.Marker({
        position: { lat: markerData.lat, lng: markerData.lng },
        map: map,
        title: markerData.name,
        // Custom icon (optional)
        icon: {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="16" cy="16" r="15" fill="#1e40af" stroke="white" stroke-width="2"/>
              <path d="M16 8L18.5 13H23L19.5 16.5L21 22L16 19L11 22L12.5 16.5L9 13H13.5L16 8Z" fill="white"/>
            </svg>
          `),
          scaledSize: new google.maps.Size(32, 32),
          anchor: new google.maps.Point(16, 32)
        }
      });

      // Add event listeners
      marker.addListener('mouseover', () => {
        setSelectedMarker(markerData);
      });

      marker.addListener('mouseout', () => {
        setSelectedMarker(null);
      });

      marker.addListener('click', () => {
        map.setCenter({ lat: markerData.lat, lng: markerData.lng });
        map.setZoom(15);
        setSelectedMarker(markerData);
      });

      return marker;
    });

    setMarkers(newMarkers);

    // Cleanup function
    return () => {
      newMarkers.forEach(marker => marker.setMap(null));
    };
  }, [map]);

  // Step 5: Handle loading and error states
  if (error) {
    return (
      <div className="container mx-auto px-6 py-8">
        <div className="bg-white rounded-lg border p-8 text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Map</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <p className="text-sm text-gray-400">
            Please check your Google Maps API key in .env.local file
          </p>
        </div>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div className="container mx-auto px-6 py-8">
        <div className="bg-white rounded-lg border p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Google Maps...</h3>
          <p className="text-gray-500">Please wait while we load the map</p>
        </div>
      </div>
    );
  }

  // Step 6: Render the map component
  return (
    <div className="container mx-auto px-6 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Simple Google Maps Demo
        </h1>
        <p className="text-gray-600">
          A beginner-friendly example showing how to integrate Google Maps with markers in Next.js
        </p>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="relative">
          {/* Map Container */}
          <div 
            ref={mapRef} 
            className="w-full h-96 bg-gray-100"
            style={{ minHeight: '400px' }}
          >
            {!isLoaded && (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-500">Loading Google Maps...</p>
                </div>
              </div>
            )}
          </div>

          {/* Simple hover information panel */}
          {selectedMarker && (
            <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg border p-4 max-w-sm">
              <div className="flex items-start space-x-3">
                <div className="bg-blue-100 p-2 rounded-lg">
                  <Shield className="w-5 h-5 text-blue-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-semibold text-gray-900 mb-1">
                    {selectedMarker.name}
                  </h3>
                  <p className="text-xs text-gray-600 mb-2">
                    {selectedMarker.address}
                  </p>

                  <div className="space-y-1">
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <Phone className="w-3 h-3" />
                      <span>{selectedMarker.phone}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <Users className="w-3 h-3" />
                      <span>{selectedMarker.officers} officers</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Simple map controls */}
        <div className="p-4 bg-gray-50 border-t">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Shield className="w-4 h-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">
                  {sampleMarkers.length} Locations
                </span>
              </div>
              <div className="text-sm text-gray-500">
                Hover over markers for details
              </div>
            </div>

            <button
              onClick={() => {
                if (map) {
                  map.setCenter({ lat: 24.7136, lng: 46.6753 });
                  map.setZoom(11);
                  setSelectedMarker(null);
                }
              }}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Reset View
            </button>
          </div>
        </div>
      </div>

      {/* Simple instructions for beginners */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">
          How to Use This Map
        </h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• <strong>Hover</strong> over blue markers to see location details</li>
          <li>• <strong>Click</strong> markers to zoom in</li>
          <li>• <strong>Drag</strong> to move around the map</li>
          <li>• <strong>Scroll</strong> to zoom in/out</li>
          <li>• Use <strong>Reset View</strong> to return to the overview</li>
        </ul>
      </div>

      {/* Developer notes */}
      <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-green-900 mb-2">
          For Developers
        </h3>
        <p className="text-sm text-green-800">
          This is a simplified example showing the basic steps to integrate Google Maps in Next.js.
          Check the component code to see the 6 main steps: setup state, load API, initialize map,
          add markers, handle loading states, and render the component.
        </p>
      </div>
    </div>
  );
}
