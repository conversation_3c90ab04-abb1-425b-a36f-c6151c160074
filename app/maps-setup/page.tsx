'use client';

import React, { useState } from 'react';

/**
 * Maps Setup Guide Page
 * Step-by-step guide for setting up Google Maps integration
 */
export default function MapsSetupPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [api<PERSON><PERSON>, setApi<PERSON>ey] = useState('');

  const steps = [
    {
      id: 1,
      title: 'Create Google Cloud Project',
      description: 'Set up a new project in Google Cloud Console',
      content: (
        <div className="space-y-4">
          <p>First, you need to create a Google Cloud project:</p>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>Go to <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google Cloud Console</a></li>
            <li>Click "Select a project" at the top</li>
            <li>Click "New Project"</li>
            <li>Enter a project name (e.g., "TTS Maps Project")</li>
            <li>Click "Create"</li>
          </ol>
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="text-blue-800 text-sm">
              💡 <strong>Tip:</strong> Make sure billing is enabled for your project to use Google Maps APIs.
            </p>
          </div>
        </div>
      ),
    },
    {
      id: 2,
      title: 'Enable Required APIs',
      description: 'Activate the necessary Google Maps APIs',
      content: (
        <div className="space-y-4">
          <p>Enable these APIs in your Google Cloud project:</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h4 className="font-semibold text-green-900 mb-2">Required</h4>
              <ul className="text-sm text-green-800 space-y-1">
                <li>✓ Maps JavaScript API</li>
              </ul>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <h4 className="font-semibold text-yellow-900 mb-2">Recommended</h4>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• Places API</li>
                <li>• Geocoding API</li>
              </ul>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-900 mb-2">Optional</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Directions API</li>
                <li>• Distance Matrix API</li>
              </ul>
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">How to enable APIs:</h4>
            <ol className="list-decimal list-inside space-y-1 text-sm">
              <li>In Google Cloud Console, go to "APIs & Services" → "Library"</li>
              <li>Search for "Maps JavaScript API"</li>
              <li>Click on it and press "Enable"</li>
              <li>Repeat for other APIs as needed</li>
            </ol>
          </div>
        </div>
      ),
    },
    {
      id: 3,
      title: 'Create API Key',
      description: 'Generate your Google Maps API key',
      content: (
        <div className="space-y-4">
          <p>Create an API key for your project:</p>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>Go to "APIs & Services" → "Credentials"</li>
            <li>Click "Create Credentials" → "API Key"</li>
            <li>Copy the generated API key</li>
            <li>Click "Restrict Key" to secure it</li>
          </ol>
          
          <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
            <h4 className="font-semibold text-red-900 mb-2">🔒 Security Important!</h4>
            <p className="text-red-800 text-sm mb-2">Always restrict your API key to prevent unauthorized usage:</p>
            <ul className="text-red-700 text-sm space-y-1 list-disc list-inside">
              <li><strong>Application restrictions:</strong> HTTP referrers (web sites)</li>
              <li><strong>API restrictions:</strong> Select only the APIs you need</li>
              <li><strong>Referrer:</strong> Add your domain (e.g., localhost:3000/* for development)</li>
            </ul>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Test your API key here:
            </label>
            <input
              type="text"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="Paste your API key here to test..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {apiKey && (
              <div className="mt-2 text-sm">
                <span className="text-green-600">✓ API key format looks correct</span>
              </div>
            )}
          </div>
        </div>
      ),
    },
    {
      id: 4,
      title: 'Configure Environment',
      description: 'Add the API key to your Next.js project',
      content: (
        <div className="space-y-4">
          <p>Add your API key to the environment variables:</p>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">1. Create .env.local file</h4>
              <p className="text-sm text-gray-600 mb-2">In your project root directory, create or edit <code className="bg-gray-100 px-1 rounded">.env.local</code>:</p>
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
                <div className="text-gray-500"># Google Maps Configuration</div>
                <div>NEXT_PUBLIC_GOOGLE_MAPS_API_KEY={apiKey || 'your_api_key_here'}</div>
                <div className="text-gray-500 mt-2"># Other environment variables</div>
                <div>NODE_ENV=development</div>
                <div>NEXT_PUBLIC_API_URL=http://localhost:3000/api</div>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-2">2. Restart Development Server</h4>
              <p className="text-sm text-gray-600 mb-2">After adding the API key, restart your Next.js development server:</p>
              <div className="bg-gray-900 text-white p-3 rounded-lg font-mono text-sm">
                <div className="text-gray-400"># Stop the current server (Ctrl+C) then run:</div>
                <div className="text-green-400">npm run dev</div>
                <div className="text-gray-400"># or</div>
                <div className="text-green-400">yarn dev</div>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-900 mb-2">⚠️ Important Notes:</h4>
            <ul className="text-yellow-800 text-sm space-y-1 list-disc list-inside">
              <li>Never commit <code>.env.local</code> to version control</li>
              <li>Use <code>NEXT_PUBLIC_</code> prefix for client-side variables</li>
              <li>Restart the server after changing environment variables</li>
            </ul>
          </div>
        </div>
      ),
    },
    {
      id: 5,
      title: 'Test Integration',
      description: 'Verify that everything is working correctly',
      content: (
        <div className="space-y-4">
          <p>Test your Google Maps integration:</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <h4 className="font-semibold text-green-900 mb-2">✅ Test Pages</h4>
              <ul className="text-green-800 text-sm space-y-1">
                <li><a href="/maps-simple" className="hover:underline">→ Simple Map Test</a></li>
                <li><a href="/maps-test" className="hover:underline">→ Advanced Features</a></li>
              </ul>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-900 mb-2">🔍 Debug Info</h4>
              <div className="text-blue-800 text-sm space-y-1">
                <div>API Key: {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? '✓ Configured' : '❌ Missing'}</div>
                <div>Environment: {process.env.NODE_ENV}</div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">Common Issues & Solutions:</h4>
            <div className="space-y-2 text-sm">
              <div>
                <strong>Map not loading:</strong> Check browser console for API key errors
              </div>
              <div>
                <strong>"RefererNotAllowedMapError":</strong> Add your domain to API key restrictions
              </div>
              <div>
                <strong>"ApiNotActivatedMapError":</strong> Enable Maps JavaScript API in Google Cloud Console
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ];

  const currentStepData = steps.find(step => step.id === currentStep);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Google Maps Setup Guide
          </h1>
          <p className="text-gray-600 text-lg">
            Follow these steps to configure Google Maps integration for your TTS application.
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium cursor-pointer ${
                    currentStep >= step.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                  onClick={() => setCurrentStep(step.id)}
                >
                  {step.id}
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`w-16 h-1 mx-2 ${
                      currentStep > step.id ? 'bg-blue-600' : 'bg-gray-200'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
          <div className="text-sm text-gray-600">
            Step {currentStep} of {steps.length}: {currentStepData?.description}
          </div>
        </div>

        {/* Current Step Content */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {currentStepData?.title}
          </h2>
          <div className="prose max-w-none">
            {currentStepData?.content}
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <button
            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
            className="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            onClick={() => setCurrentStep(Math.min(steps.length, currentStep + 1))}
            disabled={currentStep === steps.length}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}
