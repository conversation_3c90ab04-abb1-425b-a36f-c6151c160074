'use client';

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { getShipmentAlerts, ShipmentAlert } from '../../api/shipment-alerts';
import { 
  ChevronDown, 
  ChevronRight, 
  AlertTriangle, 
  AlertCircle, 
  Info, 
  Clock, 
  CheckCircle, 
  Eye,
  MapPin,
  User,
  Calendar,
  Hash,
  FileText
} from 'lucide-react';

export default function ListDemo() {
  const { t } = useLanguage();
  const [alerts, setAlerts] = useState<ShipmentAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  useEffect(() => {
    const fetchAlerts = async () => {
      try {
        setLoading(true);
        const response = await getShipmentAlerts();
        setAlerts(response.alerts);
      } catch (err) {
        setError('Failed to load alerts data');
        console.error('Error fetching alerts:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchAlerts();
  }, []);

  const toggleRowExpansion = (alertId: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(alertId)) {
      newExpandedRows.delete(alertId);
    } else {
      newExpandedRows.add(alertId);
    }
    setExpandedRows(newExpandedRows);
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      case 'info':
        return <Info className="w-4 h-4 text-blue-600" />;
      default:
        return <Info className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-orange-500" />;
      case 'investigating':
        return <Eye className="w-4 h-4 text-blue-500" />;
      case 'resolved':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'monitoring':
        return <Eye className="w-4 h-4 text-purple-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'investigating':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'resolved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'monitoring':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityBadgeStyle = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="container mx-auto px-6 py-8">
        <div className="bg-white rounded-lg border p-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-100 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-6 py-8">
        <div className="bg-white rounded-lg border p-8 text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Data</h3>
          <p className="text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-6 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Master-Detail List Demo
        </h1>
        <p className="text-gray-600">
          Demonstration of expandable table rows with detailed information. Click the "More Info" button to expand each row.
        </p>
      </div>

      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">

                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Alert
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Time
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {alerts.map((alert) => {
                const isExpanded = expandedRows.has(alert.id);
                return (
                  <React.Fragment key={alert.id}>
                    {/* Main Row */}
                    <tr className="hover:bg-gray-50 transition-colors">
                      {/* Expand/Collapse Icon Button */}
                      <td className="px-6 py-4 w-12">
                        <button
                          onClick={() => toggleRowExpansion(alert.id)}
                          className="inline-flex items-center justify-center w-8 h-8 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                          aria-expanded={isExpanded}
                          aria-label={isExpanded ? 'Collapse details' : 'Expand details'}
                        >
                          {isExpanded ? (
                            <ChevronDown className="w-5 h-5" />
                          ) : (
                            <ChevronRight className="w-5 h-5" />
                          )}
                        </button>
                      </td>

                      {/* Alert Information */}
                      <td className="px-6 py-4">
                        <div className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            {getAlertIcon(alert.type)}
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="text-sm font-medium text-gray-900 mb-1">
                              {alert.title}
                            </div>
                            <div className="text-sm text-gray-500 truncate">
                              {alert.description.substring(0, 60)}...
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`
                          inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border
                          ${getStatusBadgeStyle(alert.status)}
                        `}>
                          {getStatusIcon(alert.status)}
                          <span className="ml-1 capitalize">{alert.status}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`
                          inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border
                          ${getPriorityBadgeStyle(alert.priority)}
                        `}>
                          <span className="capitalize">{alert.priority}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatTimestamp(alert.timestamp)}
                      </td>
                    </tr>
                    
                    {/* Expandable Detail Row */}
                    {isExpanded && (
                      <tr className="bg-gray-50">
                        <td colSpan={5} className="px-6 py-4">
                          <div className="animate-in slide-in-from-top-2 duration-200">
                            <div className="bg-white rounded-lg border p-6">
                              <h4 className="text-lg font-medium text-gray-900 mb-4">
                                Alert Details
                              </h4>
                              
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {/* Full Description */}
                                <div className="lg:col-span-2">
                                  <div className="flex items-start space-x-2 mb-2">
                                    <FileText className="w-4 h-4 text-gray-400 mt-0.5" />
                                    <span className="text-sm font-medium text-gray-700">Description</span>
                                  </div>
                                  <p className="text-sm text-gray-600 leading-relaxed">
                                    {alert.description}
                                  </p>
                                </div>
                                
                                {/* Shipment Info */}
                                <div>
                                  <div className="flex items-center space-x-2 mb-2">
                                    <Hash className="w-4 h-4 text-gray-400" />
                                    <span className="text-sm font-medium text-gray-700">Shipment ID</span>
                                  </div>
                                  <p className="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded">
                                    {alert.shipmentId}
                                  </p>
                                </div>
                                
                                {/* Location */}
                                <div>
                                  <div className="flex items-center space-x-2 mb-2">
                                    <MapPin className="w-4 h-4 text-gray-400" />
                                    <span className="text-sm font-medium text-gray-700">Location</span>
                                  </div>
                                  <p className="text-sm text-gray-600">{alert.location}</p>
                                </div>
                                
                                {/* Assigned To */}
                                <div>
                                  <div className="flex items-center space-x-2 mb-2">
                                    <User className="w-4 h-4 text-gray-400" />
                                    <span className="text-sm font-medium text-gray-700">Assigned To</span>
                                  </div>
                                  <p className="text-sm text-gray-600">{alert.assignedTo}</p>
                                </div>
                                
                                {/* Timestamp */}
                                <div>
                                  <div className="flex items-center space-x-2 mb-2">
                                    <Calendar className="w-4 h-4 text-gray-400" />
                                    <span className="text-sm font-medium text-gray-700">Created</span>
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    {new Date(alert.timestamp).toLocaleString()}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
      
      <div className="mt-6 text-center text-sm text-gray-500">
        <p>
          This demo shows {alerts.length} alerts with expandable detail views. 
          Click "More Info" to see additional information for each alert.
        </p>
      </div>
    </div>
  );
}
