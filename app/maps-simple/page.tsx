'use client';

import React, { useState } from 'react';
import InteractiveMap from '@/components/maps/InteractiveMap';
import MapLoader from '@/components/maps/MapLoader';
import { MapMarker } from '@/lib/maps/types';

/**
 * Simple Maps Test Page
 * Basic test page for Google Maps integration without complex features
 */
export default function SimpleMapsPage() {
  const [selectedMarker, setSelectedMarker] = useState<MapMarker | null>(null);

  // Simple markers without complex icons
  const sampleMarkers: MapMarker[] = [
    {
      id: '1',
      position: { lat: 24.7136, lng: 46.6753 }, // Riyadh
      title: 'Riyadh - Capital',
      infoWindow: {
        content: (
          <div className="p-3">
            <h3 className="font-semibold text-lg">Riyadh</h3>
            <p className="text-gray-600">Capital of Saudi Arabia</p>
            <p className="text-sm text-gray-500 mt-1">
              Coordinates: 24.7136°N, 46.6753°E
            </p>
          </div>
        ),
      },
    },
    {
      id: '2',
      position: { lat: 21.3891, lng: 39.8579 }, // Mecca
      title: 'Mecca - Holy City',
      infoWindow: {
        content: (
          <div className="p-3">
            <h3 className="font-semibold text-lg">Mecca</h3>
            <p className="text-gray-600">Holy City</p>
            <p className="text-sm text-gray-500 mt-1">
              Coordinates: 21.3891°N, 39.8579°E
            </p>
          </div>
        ),
      },
    },
    {
      id: '3',
      position: { lat: 26.3351, lng: 43.9681 }, // Hail
      title: 'Hail - Northern Region',
      infoWindow: {
        content: (
          <div className="p-3">
            <h3 className="font-semibold text-lg">Hail</h3>
            <p className="text-gray-600">Northern Region</p>
            <p className="text-sm text-gray-500 mt-1">
              Coordinates: 26.3351°N, 43.9681°E
            </p>
          </div>
        ),
      },
    },
  ];

  // Handle marker click
  const handleMarkerClick = (marker: MapMarker) => {
    setSelectedMarker(marker);
    console.log('Marker clicked:', marker);
  };

  // Handle map click
  const handleMapClick = (event: google.maps.MapMouseEvent) => {
    if (event.latLng) {
      const position = {
        lat: event.latLng.lat(),
        lng: event.latLng.lng(),
      };
      console.log('Map clicked at:', position);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Simple Google Maps Test
          </h1>
          <p className="text-gray-600 text-lg">
            Basic Google Maps integration test with simple markers and interactions.
          </p>
        </div>

        {/* Map Container */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="mb-4">
            <h2 className="text-xl font-semibold mb-2">Interactive Map</h2>
            <p className="text-gray-600 text-sm">
              Click on markers to see info windows. Click anywhere on the map to see coordinates in console.
            </p>
          </div>

          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <MapLoader
              fallback={
                <div className="flex items-center justify-center h-96 bg-gray-50">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-gray-600">Loading Google Maps...</p>
                  </div>
                </div>
              }
              onLoadError={(error) => {
                console.error('Map load error:', error);
              }}
            >
              <InteractiveMap
                center={{ lat: 24.7136, lng: 46.6753 }}
                zoom={6}
                height="500px"
                markers={sampleMarkers}
                onMarkerClick={handleMarkerClick}
                onClick={handleMapClick}
                enableClustering={false}
                showZoomControl={true}
                showMapTypeControl={true}
                showStreetViewControl={true}
                showFullscreenControl={true}
                theme="default"
                className="simple-map"
              />
            </MapLoader>
          </div>

          {/* Selected Marker Info */}
          {selectedMarker && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Selected Marker</h3>
              <div className="text-sm text-blue-800">
                <p><strong>Title:</strong> {selectedMarker.title}</p>
                <p><strong>Position:</strong> {selectedMarker.position.lat.toFixed(4)}, {selectedMarker.position.lng.toFixed(4)}</p>
                <p><strong>ID:</strong> {selectedMarker.id}</p>
              </div>
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-green-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-green-900 mb-3">
              ✅ Working Features
            </h3>
            <ul className="text-green-800 space-y-2 text-sm">
              <li>• Basic Google Maps integration</li>
              <li>• Simple markers with info windows</li>
              <li>• Map click event handling</li>
              <li>• Responsive design</li>
              <li>• Error handling and loading states</li>
              <li>• Map controls (zoom, type, street view)</li>
            </ul>
          </div>

          <div className="bg-yellow-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-yellow-900 mb-3">
              ⚠️ Setup Required
            </h3>
            <div className="text-yellow-800 space-y-2 text-sm">
              <p>
                <strong>1. API Key:</strong> Add your Google Maps API key to environment variables:
              </p>
              <div className="bg-yellow-100 p-2 rounded font-mono text-xs">
                NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_key_here
              </div>
              <p>
                <strong>2. Enable APIs:</strong> Make sure these APIs are enabled in Google Cloud Console:
              </p>
              <ul className="list-disc list-inside ml-2">
                <li>Maps JavaScript API</li>
                <li>Places API (optional)</li>
                <li>Geocoding API (optional)</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Debug Info */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Debug Information</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <p><strong>API Key Status:</strong> {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? 'Configured' : 'Missing'}</p>
            <p><strong>Markers Count:</strong> {sampleMarkers.length}</p>
            <p><strong>Selected Marker:</strong> {selectedMarker?.title || 'None'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
