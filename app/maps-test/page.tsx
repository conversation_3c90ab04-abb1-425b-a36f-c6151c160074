"use client";

import React, { useState } from "react";
import MapExample from "@/components/maps/MapExample";
import VehicleTrackingMap from "@/components/maps/VehicleTrackingMap";
import TripRouteMap from "@/components/maps/TripRouteMap";
import GeofenceManagementMap from "@/components/maps/GeofenceManagementMap";
import { Vehicle, TripData, Geofence } from "@/lib/maps/types";

/**
 * Maps Test Page
 * Test page for Google Maps integration
 */
export default function MapsTestPage() {
  const [activeTab, setActiveTab] = useState("basic");

  // Sample data for testing
  const sampleVehicles: Vehicle[] = [
    {
      id: "1",
      name: "Vehicle 001",
      position: { lat: 24.7136, lng: 46.6753 },
      heading: 45,
      speed: 60,
      status: "moving",
      lastUpdate: new Date(),
      driver: { name: "<PERSON>", id: "driver1" },
    },
    {
      id: "2",
      name: "Vehicle 002",
      position: { lat: 24.72, lng: 46.68 },
      heading: 180,
      speed: 0,
      status: "idle",
      lastUpdate: new Date(),
      driver: { name: "<PERSON>", id: "driver2" },
    },
  ];

  const sampleTrip: TripData = {
    id: "trip1",
    name: "Riyadh to Mecca Route",
    origin: { lat: 24.7136, lng: 46.6753 },
    destination: { lat: 21.3891, lng: 39.8579 },
    waypoints: [
      { lat: 24.5, lng: 46.0 },
      { lat: 23.0, lng: 42.0 },
    ],
    currentPosition: { lat: 24.6, lng: 46.5 },
    status: "active",
    distance: 870,
    estimatedDuration: 540,
    plannedRoute: [
      { lat: 24.7136, lng: 46.6753 },
      { lat: 24.5, lng: 46.0 },
      { lat: 23.0, lng: 42.0 },
      { lat: 21.3891, lng: 39.8579 },
    ],
    completedRoute: [
      { lat: 24.7136, lng: 46.6753 },
      { lat: 24.6, lng: 46.5 },
    ],
  };

  const sampleGeofences: Geofence[] = [
    {
      id: "geo1",
      name: "Riyadh Center",
      type: "circle",
      coordinates: [{ lat: 24.7136, lng: 46.6753 }],
      radius: 5000,
      color: "#2196F3",
      strokeColor: "#1976D2",
      fillOpacity: 0.4,
      strokeWeight: 2,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Google Maps Integration Test
          </h1>
          <p className="text-gray-600 text-lg">
            This page demonstrates the Google Maps integration with interactive
            features, markers, and real-time capabilities.
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: "basic", name: "Basic Map" },
                { id: "vehicles", name: "Vehicle Tracking" },
                { id: "trips", name: "Trip Routes" },
                { id: "geofences", name: "Geofences" },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          {activeTab === "basic" && <MapExample />}

          {activeTab === "vehicles" && (
            <div>
              <h2 className="text-xl font-semibold mb-4">
                Vehicle Tracking Map
              </h2>
              <VehicleTrackingMap
                center={{ lat: 24.7136, lng: 46.6753 }}
                zoom={12}
                height="500px"
                vehicles={sampleVehicles}
                showRoutes={true}
                showVehicleInfo={true}
                onVehicleSelect={(vehicle) =>
                  console.log("Selected vehicle:", vehicle)
                }
              />
            </div>
          )}

          {activeTab === "trips" && (
            <div>
              <h2 className="text-xl font-semibold mb-4">Trip Route Map</h2>
              <TripRouteMap
                center={{ lat: 23.5, lng: 43.0 }}
                zoom={7}
                height="500px"
                trip={sampleTrip}
                showWaypoints={true}
                showProgress={true}
                showAlerts={true}
                showETA={true}
              />
            </div>
          )}

          {activeTab === "geofences" && (
            <div>
              <h2 className="text-xl font-semibold mb-4">
                Geofence Management
              </h2>
              <GeofenceManagementMap
                center={{ lat: 24.7136, lng: 46.6753 }}
                zoom={12}
                height="500px"
                geofences={sampleGeofences}
                editMode={true}
                showGeofenceInfo={true}
                onGeofenceCreate={(geofence) =>
                  console.log("Created geofence:", geofence)
                }
                onGeofenceEdit={(geofence) =>
                  console.log("Edited geofence:", geofence)
                }
                onGeofenceDelete={(id) => console.log("Deleted geofence:", id)}
              />
            </div>
          )}
        </div>

        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-blue-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">
              🗺️ Features Implemented
            </h3>
            <ul className="text-blue-800 space-y-2">
              <li>✅ Interactive Google Maps</li>
              <li>✅ Custom markers with info windows</li>
              <li>✅ Map click event handling</li>
              <li>✅ Zoom and bounds change detection</li>
              <li>✅ Marker clustering support</li>
              <li>✅ Polylines and polygons</li>
              <li>✅ Circle overlays</li>
              <li>✅ Error handling and loading states</li>
            </ul>
          </div>

          <div className="bg-green-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-green-900 mb-3">
              🚀 Next Steps
            </h3>
            <ul className="text-green-800 space-y-2">
              <li>🔄 Real-time vehicle tracking</li>
              <li>🛣️ Route visualization</li>
              <li>📍 Geofence management</li>
              <li>📊 Dashboard overview map</li>
              <li>🎨 Custom map themes</li>
              <li>📱 Mobile optimization</li>
              <li>⚡ Performance optimization</li>
            </ul>
          </div>
        </div>

        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-900 mb-3">
            ⚠️ Setup Requirements
          </h3>
          <div className="text-yellow-800 space-y-2">
            <p>
              <strong>1. Google Maps API Key:</strong> Make sure you have set up
              your Google Maps API key in the environment variables.
            </p>
            <p>
              <strong>2. Environment Variable:</strong> Add{" "}
              <code className="bg-yellow-100 px-2 py-1 rounded">
                NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_api_key_here
              </code>{" "}
              to your .env.local file.
            </p>
            <p>
              <strong>3. API Restrictions:</strong> Configure your API key
              restrictions in the Google Cloud Console for security.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
